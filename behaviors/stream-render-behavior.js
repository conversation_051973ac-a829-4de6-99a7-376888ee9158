/**
 * 流式渲染行为
 * 处理 WebSocket 流式响应的数据接收与 UI 渲染。
 *
 * 关键改动 (基于 2025-05-03 & Subsequent Fixes):
 * ... (previous changelog entries)
 * 12. [ADDED] infoHash validation in _handleAnalysisChunkThrottled, _handleAnalysisStatus, and _handleResponse callbacks.
 * 13. [REFINED] Ensure socketClient.acquire() and socketClient.release() use this.socketClient (from globalData) for instance consistency.
 */

// const socketClient = require('../utils/socket-client'); // ⚠️ This direct import for acquire/release is being phased out for consistency.

module.exports = Behavior({
    data: {
      isLoading: true,             // 是否正在加载中
      isStreaming: false,          // 是否正在接收流式内容
      error: null,                 // 错误信息
      analysisStep: '',            // 仅用于错误或特殊状态提示，不做进度展示
      content: '',                 // 原始内容 (conclusion)
      reasoningContent: '',        // 原始推理内容
      result: '',                  // 格式化后的 HTML 内容 (conclusion)
      reasoningResult: '',         // 格式化后的推理 HTML 内容
      infoHash: '',                // 当前分析的 infoHash
      paymentInfo: null,           // 付费信息
      isSocketReady: false,        // WebSocket 是否准备好
      showPaymentButton: false,     // 是否显示付费按钮
      isDuplicateRequest: false    // 是否重复请求
    },

    lifetimes: {
      /**
       * Behavior attached, similar to onLoad/created.
       */
      attached() {
        this.logger = getApp().globalData.logger || console;
        
        // Initialize this.socketClient from globalData first
        this.socketClient = getApp().globalData.socketClient;
        if (!this.socketClient) {
          this.logger.error('stream-render-behavior attached: Global socketClient not found!');
          // Potentially set an error state or prevent further initialization
          return;
        }

        this._onAnalysisStatus = this._handleAnalysisStatus.bind(this);
        this._onAnalysisChunk = this._handleAnalysisChunkThrottled.bind(this);
        this._onResponse       = this._handleResponse.bind(this);
        this._onSocketError    = this._handleSocketError.bind(this);

        this._initChunkBuffer();
        this.initSocketClient(); // This will use this.socketClient for event setup

        // Use this.socketClient for acquire
        if (typeof this.socketClient.acquire === 'function') {
          this.socketClient.acquire();
          this.logger.debug('stream-render-behavior attached: this.socketClient.acquire() called.');
        } else {
          this.logger.error('stream-render-behavior attached: this.socketClient.acquire is not a function!');
        }
      },

      /**
       * Behavior detached, similar to onUnload.
       */
      detached() {
        this.cleanupResources(); // This will call this.socketClient.release()
      }
    },

    methods: {
      _initChunkBuffer() {
        this.chunkBuffer = { reasoning: [], conclusion: [] };
        if (this.flushTimer) {
          clearTimeout(this.flushTimer);
        }
        this.flushTimer = null;
        this.logger.debug('Chunk buffer initialized');
      },

      initSocketClient() {
        // this.socketClient is already initialized in attached() from getApp().globalData.socketClient
        if (!this.socketClient) {
          this.logger.error('stream-render-behavior initSocketClient: this.socketClient is not initialized. Attempting to re-initialize.');
          // Fallback, though ideally it should be set in attached
          this.socketClient = getApp().globalData.socketClient;
          if (!this.socketClient) {
            this.logger.error('stream-render-behavior initSocketClient: Failed to get global socketClient.');
            this.setData({ error: 'WebSocket 连接未初始化', isLoading: false });
            return;
          }
        }

        this.setData({ isSocketReady: this.socketClient.isSocketReady ? this.socketClient.isSocketReady() : true }); // Assuming isSocketReady method or direct property

        this._setupSocketEvents(); // Uses this.socketClient
        this.logger.debug('Socket client event listeners setup using this.socketClient.');
      },

      async startAnalysis(userData) {
        if (!userData || !userData.infoHash) {
          this.setData({ error: '缺少必要的用户信息', isLoading: false });
          return false;
        }

        this.setData({ infoHash: userData.infoHash });
        this._resetState(); 

        if (!this.socketClient || !(this.socketClient.isSocketReady ? this.socketClient.isSocketReady() : this.data.isSocketReady)) {
          this.setData({ error: 'WebSocket 连接未就绪，请刷新页面重试', isLoading: false });
          wx.showToast({ title: 'WebSocket 连接未就绪，请刷新页面重试', icon: 'none', duration: 2000 });
          return false;
        }

        try {
          // Assuming sendAnalyzeRequest is a method of this.socketClient
          await this.socketClient.sendAnalyzeRequest(userData);
          this.setData({ isStreaming: true, analysisStep: '' });
          return true;
        } catch (error) {
          this.logger.error('发送分析请求失败:', error);
          this._handleError(error);
          return false;
        }
      },

      async requestSecondStage() {
        if (!this.data.infoHash) {
          this._handleError(new Error('缺少必要的 infoHash 参数'));
          return false;
        }

        if (!this.socketClient || !(this.socketClient.isSocketReady ? this.socketClient.isSocketReady() : this.data.isSocketReady)) {
          this._handleError(new Error('WebSocket 连接未就绪，请刷新页面重试'));
          return false;
        }

        this._resetState();
        this.setData({
          isLoading: true,
          isStreaming: true,
          analysisStep: '',
        });

        try {
          // Assuming requestSecondStage is a method of this.socketClient
          await this.socketClient.requestSecondStage({ infoHash: this.data.infoHash });
          return true;
        } catch (error) {
          this.logger.error('请求第二阶段分析失败:', error);
          this._handleError(error);
          return false;
        }
      },

      _setupSocketEvents() {
        if (!this.socketClient) {
            this.logger.warn('_setupSocketEvents: this.socketClient is null, cannot setup events.');
            return;
        }

        const cli = this.socketClient; // Use the instance stored in this.socketClient
        cli.off('analysisStatus', this._onAnalysisStatus);
        cli.off('analysisChunk', this._onAnalysisChunk);
        cli.off('response', this._onResponse);
        cli.off('error', this._onSocketError);

        cli.on('analysisStatus', this._onAnalysisStatus);
        cli.on('analysisChunk', this._onAnalysisChunk);
        cli.on('response', this._onResponse);
        cli.on('error', this._onSocketError);

        this.logger.debug('WebSocket 事件监听设置完成 (using this.socketClient and cached handlers)');
      },

      cleanupResources() {
        this.logger.debug('Cleaning up stream-render-behavior resources...');
        
        // Use this.socketClient for release
        if (this.socketClient && typeof this.socketClient.release === 'function') {
          this.socketClient.release();
          this.logger.debug('stream-render-behavior cleanupResources: this.socketClient.release() called.');
        } else {
          this.logger.warn('stream-render-behavior cleanupResources: this.socketClient.release is not a function or socketClient is null.');
        }

        if (this.flushTimer) {
          clearTimeout(this.flushTimer);
          this.flushTimer = null;
          this.logger.debug('Flush timer cleared.');
        }
        this._initChunkBuffer();

        if (this.socketClient) {
          this.socketClient.off('analysisStatus', this._onAnalysisStatus);
          this.socketClient.off('analysisChunk', this._onAnalysisChunk);
          this.socketClient.off('response', this._onResponse);
          this.socketClient.off('error', this._onSocketError);
          this.logger.debug('WebSocket event listeners removed (using this.socketClient and cached handlers).');
        }

        this._onAnalysisStatus = null;
        this._onAnalysisChunk = null;
        this._onResponse = null;
        this._onSocketError = null;
      },

      _handleAnalysisStatus(data) {
        if (data?.data?.infoHash && data.data.infoHash !== this.data.infoHash) {
          this.logger.debug(
            `Skip status update – infoHash mismatch (${data.data.infoHash} ≠ ${this.data.infoHash})`
          );
          return;
        }

        if (!data || !data.data) return;
        const { stage, status } = data.data;

        if (stage === 'first' && status === 'completed') {
          this.logger.info('分析完成，显示付费按钮');
          if (this.flushTimer) {
            clearTimeout(this.flushTimer);
            this.flushTimer = null;
          }
          this._flushChunkBuffer();
          this.setData({
            isLoading: false,
            isStreaming: false,
            analysisStep: '基础分析完成',
            showPaymentButton: true,
          });

          try {
            const userInfo = wx.getStorageSync('userInfo');
            if (userInfo) {
              userInfo.firstStageCompleted = true;
              wx.setStorageSync('userInfo', userInfo);
              this.logger.info('已将第一阶段完成状态保存到用户信息');
            }
          } catch (err) {
            this.logger.error('保存第一阶段完成状态失败:', err);
          }
        }
      },

      _handleAnalysisChunkThrottled(payload) {
          if (payload?.data?.infoHash && payload.data.infoHash !== this.data.infoHash) {
            this.logger.debug(
              `Skip chunk – infoHash mismatch (${payload.data.infoHash} ≠ ${this.data.infoHash})`
            );
            return;
          }

          if (this.data.isDuplicateRequest) {
            this.setData({
              error: null,
              analysisStep: '',
              isDuplicateRequest: false
            });
          }

          if (!payload || !payload.data) {
            this.logger.warn('Received invalid chunk payload structure (missing data object)');
            return;
          }

          const { type, content } = payload.data;

          if (content === null || content === undefined || content === '') {
            this.logger.debug(`Skipping chunk with empty content (type: ${type})`);
            return;
          }

          this.setData({ isStreaming: true, isLoading: false });

          const bucket = type === 'reasoning' ? 'reasoning' : 'conclusion';
          this.chunkBuffer[bucket].push(content);

          if (!this.flushTimer) {
            this.flushTimer = setTimeout(() => {
              this._flushChunkBuffer();
            }, 60);
          }
      },


      /**
       * 批量 setData 更新 UI
       */
      _flushChunkBuffer() {
        const { reasoning, conclusion } = this.chunkBuffer;
        const dataToSet = {};
        let didUpdate = false;

        // 1) 拼接 reasoning
        if (reasoning.length > 0) {
          const reasoningDelta = reasoning.join('');
          const newReasoningRaw = (this.data.reasoningContent || '') + reasoningDelta;
          const formattedReasoning = this.formatContent(newReasoningRaw);
          const htmlReasoning = this.convertMarkdownToHtml(formattedReasoning);
          dataToSet.reasoningContent = newReasoningRaw;
          dataToSet.reasoningResult = htmlReasoning;
          this.chunkBuffer.reasoning.length = 0; // 清空已处理部分
          didUpdate = true;
        }

        // 2) 拼接 conclusion
        if (conclusion.length > 0) {
          const conclusionDelta = conclusion.join('');
          const newContentRaw = (this.data.content || '') + conclusionDelta;
          const formattedContent = this.formatContent(newContentRaw);
          const htmlContent = this.convertMarkdownToHtml(formattedContent);
          dataToSet.content = newContentRaw;
          dataToSet.result = htmlContent;
          this.chunkBuffer.conclusion.length = 0; // 清空已处理部分
          didUpdate = true;
        }

        // 3) 执行 setData (仅当有更新时)
        if (didUpdate) {
          this.setData(dataToSet);
        }

        // 4) 清空定时器标记 (重要！)
        this.flushTimer = null;
      },


      /**
       * 处理一般响应 (非流式块)
       */
      _handleResponse(data) {
        if (data?.data?.infoHash && data.data.infoHash !== this.data.infoHash) {
            this.logger.debug(
              `Skip response – infoHash mismatch (${data.data.infoHash} ≠ ${this.data.infoHash})`
            );
            return;
        }

        if (!data) return;
        this.logger.debug('收到非流式响应:', data);

        if (data.code === 40001 || data.code === 202 ||
            (data.data && data.data.status === 'processing')) {
          wx.showToast({
            title: '分析排队中，请稍候…',
            icon: 'none',
            duration: 1500
          });
          this.setData({
            isLoading: true,
            isStreaming: false,
            isDuplicateRequest: true
          });
          return;
        }

        const success = data.success !== undefined ? data.success : (data.code === 200 || data.code === 0);

        if (success) {
          if (data.data) {
             if (data.data.firstStage) {
               const fst = data.data.firstStage;
               let content = '', reasoningContent = '';
               if (typeof fst === 'object') {
                 content = fst.content || '';
                 reasoningContent = fst.reasoningContent || '';
               } else if (typeof fst === 'string') {
                 content = fst;
               }
               if (content && content !== this.data.content) {
                 const fmt = this.formatContent(content);
                 this.setData({ 
                   content, 
                   result: this.convertMarkdownToHtml(fmt),
                   showPaymentButton: true,
                   isStreaming: false,
                   isLoading: false
                 });
               }
               if (reasoningContent && reasoningContent !== this.data.reasoningContent) {
                 const fmtR = this.formatContent(reasoningContent);
                 this.setData({ reasoningContent, reasoningResult: this.convertMarkdownToHtml(fmtR) });
               }
             } else if (data.data.content) {
               const content = data.data.content || '';
               if (content && content !== this.data.content) {
                 const fmt = this.formatContent(content);
                 this.setData({ 
                   content, 
                   result: this.convertMarkdownToHtml(fmt),
                   showPaymentButton: true,
                   isStreaming: false,
                   isLoading: false
                 });
               }
             }

             const needPayment = data.data.paymentRequired;
             if (needPayment) {
               this.setData({
                 showPaymentButton: true,
                 paymentInfo: data.data.paymentInfo || { price: '19.9', currency: 'CNY', description: '解锁高级八字分析，包含大运流年详解' },
               });
               if (!this.data.isStreaming) {
                   this.setData({
                       isLoading: false,
                       isStreaming: false,
                       analysisStep: data.data.paymentRequired ? '基础分析完成' : '分析完成'
                   });
               }
             }  
          } 
        } else {
          const errMsg = data.message || '请求出错';
          this._handleError(new Error(errMsg));
        }
      },

      _handleSocketError(error) {
        this.logger.error('WebSocket错误:', error);
        if (this.socketClient && error && error.message && error.message.includes('连接')) {
             // this.setData({ isSocketReady: false }); // isSocketReady should reflect actual connection state
             // It's better if socketClient itself manages its ready state internally and exposes it
        }
        this._handleError(error);
      },

      /**
       * 通用错误处理
       */
      _handleError(error) {
        this.logger.error('Handling error:', error);
        if (this.flushTimer) {
            clearTimeout(this.flushTimer);
            this.flushTimer = null;
        }
        this.setData({
            isLoading: false,
            isStreaming: false,
            error: error.message || '分析过程中出现未知错误',
            analysisStep: '分析失败' // 错误状态提示
        });
        wx.showToast({ title: error.message || '分析失败', icon: 'none', duration: 2500 });
      },

      /**
       * 重置状态 (用于开始新分析前)
       */
      _resetState() {
        if (this.flushTimer) {
          clearTimeout(this.flushTimer);
          this.flushTimer = null;
        }
        this._initChunkBuffer();

        this.setData({
          isLoading: true,
          isStreaming: false,
          error: null,
          content: '',
          reasoningContent: '',
          result: '',
          reasoningResult: '',
          analysisStep: '', // 初始状态下不显示任何提示
          paymentInfo: null,
          showPaymentButton: false,
          isDuplicateRequest: false
        });
        this.logger.debug('State reset for new analysis.');
      },

      /**
       * App 从后台返回前台时更新处理 - 简化，仅在有错误状态时显示
       */
       _updateStepOnAppShow() {
         // 仅在出错时或特殊状态下保留状态显示，不再更新普通进度
         if (this.data.error || this.data.analysisStep.includes('完成') || this.data.analysisStep.includes('失败')) {
            this.logger.debug('Preserved error or special state on app show:', this.data.analysisStep);
         }
       },

      // --- 内容格式化与转换 --- (保留原有逻辑)

      /**
       * 格式化文本内容 (简单清理)
       */
      formatContent(text) {
          if (!text) return '';
          
          // 清理基本格式问题
          text = text.replace(/\r/g, '');
          
          // 预处理一、二、三标题格式
          text = text.replace(/^([一二三四五六七八九十]+)[、：:.](.*)$/gm, (match, num, content) => {
            // 确保标题后至少有一个空行
            return `\n\n${num}、${content.trim()}\n\n`;
          });
          
          // 处理破折号 - 确保前后有空格并优化样式
          text = text.replace(/([^\n])—/g, '$1 —');
          text = text.replace(/—([^\n])/g, '— $1');
          
          // 在划分段落上参考simple-result
          const lines = text.split('\n');
          const processedLines = [];
          
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 处理中文数字标题，确保前后有空行
            if (line.match(/^[一二三四五六七八九十]+[、：:.]/)) {
              // 如果前一行不是空行，则添加空行
              if (processedLines.length > 0 && processedLines[processedLines.length - 1] !== '') {
                processedLines.push('');
              }
              
              processedLines.push(line);
              if (i < lines.length - 1 && lines[i + 1].trim() !== '') { 
                processedLines.push('');
              }
              continue;
            }
            
            // 普通行处理
            processedLines.push(line);
          }
          
          // 重组文本并确保段落之间只有一个空行
          text = processedLines.join('\n');
          text = text.replace(/\n{3,}/g, '\n\n');
          
          return text.trim();
      },

      /**
       * 转换 Markdown 为 HTML (简化版)
       */
      convertMarkdownToHtml(markdown) {
          if (!markdown) return '';
          
          try {
            let html = markdown;
            
            // 特别处理中文数字标题（一、二、三...）- 参考simple-result
            html = html.replace(/^([一二三四五六七八九十]+)[、：:.](?:\s*)(.*?)$/gm, (match, num, content) => {
              const trimmedContent = content.trim();
              if (trimmedContent) {
                return `<h3 class="chinese-num-title">${num}、${trimmedContent}</h3>`;
              }
              return match;
            });
            
            // 标准Markdown处理
            html = html.replace(/^###### (.*$)/gm, '<h6>$1</h6>');
            html = html.replace(/^##### (.*$)/gm, '<h5>$1</h5>');
            html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
            html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
            html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
            html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
            
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            // 处理破折号 - 带样式的换行元素
            html = html.replace(/([^<])\s*—\s*/g, '$1<br><span class="dash-line-break">—</span> ');
            
            // 在段中添加分隔符 - 每3-4个段落添加一个华丽的分隔符
            const paragraphs = html.split(/\n\n+/);
            const formattedParagraphs = [];
            
            paragraphs.forEach((paragraph, index) => {
              const trimmedPara = paragraph.trim();
              
              // 跳过空段落
              if (!trimmedPara) return;
              
              // 如果是标题或已有特殊样式，直接添加
              if (trimmedPara.match(/^<(h[1-6]|div|blockquote)/) || 
                  trimmedPara.match(/<h3 class="chinese-num-title">/)) {
                formattedParagraphs.push(paragraph);
                return;
              }
              
              // 普通段落处理
              formattedParagraphs.push(`<p>${trimmedPara.replace(/\n/g, '<br>')}</p>`);
              
              // 每3-4个段落后添加分隔符 (但避免在标题前后添加)
              const nextParagraph = paragraphs[index + 1]?.trim();
              if (index > 0 && (index + 1) % 4 === 0 && index < paragraphs.length - 2 &&
                  !nextParagraph?.match(/^<(h[1-6]|div|blockquote)/) && 
                  !nextParagraph?.match(/^[一二三四五六七八九十]+[、：:.]/)) {
                formattedParagraphs.push('<div class="content-separator"></div>');
              }
            });
            
            html = formattedParagraphs.join('\n');
            return html;
          } catch (err) {
            this.logger.error('Markdown to HTML conversion error:', err);
            return `<p>${markdown.replace(/\n/g, '<br>')}</p>`;
          }
      }
    }
});