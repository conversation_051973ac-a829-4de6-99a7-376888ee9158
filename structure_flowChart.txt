flowchart LR
 subgraph subGraph0["WeChat Mini-Program (Client)"]
        Intro["引导页"]
        Login["注册/登录页"]
        Form["信息表单页 (地址 & 出生信息)"]
        Main["功能页 - 星座·紫薇·八字…"]
        UserCenter["用户中心/订单"]
  end
 subgraph subGraph1["Mini-Program SDK"]
        HttpClient("HTTPS Client")
        WsClient("WebSocket Client")
  end
 subgraph subGraph2["Node.js Container Cluster"]
        AuthSvc("Auth Service")
        GeoSvc("Geo Service")
        AnalysisSvc("Analysis Service")
        Prompt("Prompt Builder")
        PaySvc("Payment Webhook")
        OrderSvc("Order Service")
        UserSvc("User Service")
  end
    Intro --> Login
    Login --> Form
    Form --> Main
    Main --> UserCenter
    Form -- HTTPS --> HttpClient
    Main -- WS /stream --> WsClient
    UserCenter -- HTTPS /user /orders --> HttpClient
    HttpClient -- /auth /geo /pay /user --> APIGW("API Gateway")
    WsClient == /stream ==> APIGW
    APIGW --> AuthSvc & GeoSvc & AnalysisSvc & PaySvc & UserSvc
    AddrDB["PostgreSQL address_geo"] -- MISS --> MapAPI["Tencent Geocoder API"]
    MapAPI --> GeoSvc
    GeoSvc -- inquire --> AddrDB
    GeoSvc -- lat,lng --> UserSvc
    UserSvc --> UserDB["PostgreSQL users"]
    AuthSvc --> UserDB
    PaySvc --> OrderSvc
    OrderSvc --> OrderDB["PostgreSQL orders"]
    AnalysisSvc --> Prompt
    AnalysisSvc -- 当前暂缓 --> Storage["S3 分析结果"] & Cache["Redis submission_cache"]
    AnalysisSvc -- 可切换 --> LLM["DeepSeek LLM"]
    PaySvc -- WeChat / Stripe / PayPal --> PaymentProvider["支付渠道"]
    MapAPI -- update --> AddrDB
    AnalysisSvc -- 当前业务 --> UserDB

     AddrDB:::store
     UserDB:::store
     OrderDB:::store
     Storage:::store
     Cache:::store
    classDef store fill:#f9f,stroke:#333,stroke-width:1px,color:#000



