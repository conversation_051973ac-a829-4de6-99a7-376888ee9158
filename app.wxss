/**app.wxss**/
/* CSS 变量定义 - 根据 Figma design token 调整这些值 */
page {
    /* 主色调紫色 背景等偏粉色调 字体用偏蓝色调*/
    --color-purple: #BE74FF; /* 紫色 */
    --color-purple-dark1:#A252E8; /* 紫色深色1 */
    --color-purple-dark2: #6B19B3; /* 紫色深色2 */
    --color-purple-light: #D7A8FF; /* 紫色浅色1 */
    --color-purple-light2:#EAD4FF; /* 紫色浅色2 */
    --color-purple-light3:#f9f1ff; /* 紫色浅色3 */
    --color-bluepurple-dark:#5635a7; /* 蓝紫色深 */
    --color-bluepurple:#6B42D1; /* 蓝紫色 */
    --color-bluepurple-light1:#a68ee3; /* 蓝紫色浅色1 */
    --color-bluepurple-light2:#e1d9f6; /* 蓝紫色浅色2 */
    --color-bluepurple-light3:#f4efff; /* 蓝紫色浅色3 */
    --color-bluepurple-light4:#f6f2ff; /* 蓝紫色浅色4 */

     /* 强调色金色 */
     --color-gold-light2:#fff0ba; /* 金色浅色2 */
     --color-gold-light:#FFE174; /* 金色浅色1*/
     --color-gold:#FFC118; /* 金色 */
     --color-gold-lightdark:#E3C763; /* 金色浅加深15% */
     --color-gold-dark:#E1AB16; /* 金色加深15% */
     
     /* 语义橙 */
     --color-orange:#ff9b1a;
     --color-orange-dark: #ff6600;

    /* 中性灰 */
    --color-bluegray-dark5:#2D2F32; /* 蓝灰色深5 */
    --color-bluegray-dark3:#838690; /* 蓝灰色深3 */
    --color-bluegray-dark2:#AFB3C0; /* 蓝灰色深2 */
    --color-bluegray-dark1:#c5cad8; /* 蓝灰色深1 */
    --color-bluegray:#DBE0F0; /* 蓝灰色 */
    --color-bluegray-light1:#e2e6f3; /* 蓝灰色浅1 */
    --color-gray-light:#d8d8d8;/* 浅浅灰 */



    /* 字体大小 */
    --font-size-large: 44rpx; /* 大字体，如标题 */
    --font-size-mediumlarge: 40rpx; /* 大字体，二级标题 */
    --font-size-medium: 36rpx; /* 中等字体，如标题二级 */
    --font-size-normal: 26rpx; /* 普通字体，如具体内容*/
    --font-size-small: 24rpx; /* 小字体，如辅助文本 */
    
    /* 间距 */
    --spacing-000: 0rpx; 
    --spacing-050: 8rpx;         
    --spacing-100: 16rpx;         
    --spacing-150: 24rpx;
    --spacing-200: 32rpx;
    --spacing-250: 40rpx;
    --spacing-300: 48rpx;
    --spacing-400: 64rpx;  
    --spacing-500: 80rpx;   
    --spacing-600: 96rpx;
    --spacing-700: 112rpx;           
    
    /* 圆角 */
    --radius-large: 24rpx;           /* 大圆角 */
    --radius-medium: 16rpx;          /* 中等圆角 */
    --radius-small: 8rpx;            /* 小圆角 */
  }

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 
