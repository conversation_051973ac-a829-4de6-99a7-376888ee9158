FROM node:18-slim 

WORKDIR /app

# 安装python3和pip  
RUN apt-get update && \
    apt-get install -y --no-install-recommends python3 python3-pip && \
    rm -rf /var/lib/apt/lists/*

# 1️⃣ 只拷贝后端依赖描述
COPY server/package*.json ./
RUN npm ci --production

# 2️⃣ 再拷贝后端源码
COPY server .

# 如果你把 Python 依赖列在 server/requirements.txt
RUN pip3 install --no-cache-dir -r requirements.txt

# ---- 环境与启动 ----
ENV PYTHONUNBUFFERED=1 NODE_ENV=production PORT=${WX_PORT:-80}
EXPOSE ${PORT}

HEALTHCHECK --interval=30s --timeout=5s --retries=3 \
  CMD wget -qO- http://127.0.0.1/healthz || exit 1

CMD ["node", "app.js"]

