const Joi = require('joi');

const analysisSchema = Joi.object({
  year: Joi.string().required(),
  month: Joi.string().required(),
  day: Joi.string().required(),
  hour: Joi.string().required(),
  minute: Joi.string().required(),
  location: Joi.string().required(),
  gender: Joi.string().required(),
  longitude: Joi.number().min(-180).max(180).allow(null, '').optional()
});

module.exports.validateAnalysis = (req, res, next) => {
  const { error } = analysisSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      code: 400,
      message: `参数校验失败: ${error.details[0].message}`
    });
  }
  next();
};