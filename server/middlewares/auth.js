const config = require('../config');

module.exports = (req, res, next) => {
  // 开发环境下跳过认证
  if (process.env.NODE_ENV === 'development') {
    return next();
  }

  // 验证请求来源
  const origin = req.headers.origin;
  if (!config.security.allowedOrigins.includes(origin)) {
    return res.status(403).json({ code: 403, message: '禁止访问' });
  }

  // 验证基础令牌
  const clientToken = req.headers['x-client-token'];
  if (clientToken !== process.env.CLIENT_TOKEN) {
    return res.status(401).json({ code: 401, message: '未授权的请求' });
  }

  next();
};