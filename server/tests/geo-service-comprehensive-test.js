/**
 * 地理位置服务综合测试
 * 测试各种场景下的地址查询功能
 */

const geoService = require('../services/geo-service');
const logger = require('../utils/logger');

// 测试用例配置
const testCases = [
  {
    category: '🏙️ 主要城市',
    addresses: [
      '北京市朝阳区',
      '上海市浦东新区', 
      '广州市番禺区',
      '深圳市福田区',
      '杭州市西湖区'
    ]
  },
  {
    category: '🌆 省会城市',
    addresses: [
      '四川省成都市',
      '湖北省武汉市',
      '陕西省西安市',
      '江苏省南京市',
      '浙江省杭州市'
    ]
  },
  {
    category: '🏘️ 地级市',
    addresses: [
      '四川省绵阳市',
      '山东省烟台市',
      '江苏省苏州市',
      '广东省佛山市',
      '河南省洛阳市'
    ]
  },
  {
    category: '🏞️ 县级市/区县',
    addresses: [
      '四川省成都市双流区',
      '北京市昌平区',
      '上海市嘉定区',
      '广东省东莞市',
      '江苏省昆山市'
    ]
  },
  {
    category: '🚫 边界测试',
    addresses: [
      '',                    // 空字符串
      '   ',                 // 空格
      '不存在的地址123',      // 不存在的地址
      'ABCDEFG',            // 英文地址
      '测试@#$%地址'         // 特殊字符
    ]
  }
];

// 统计信息
let totalTests = 0;
let successfulTests = 0;
let failedTests = 0;
let cacheHits = 0;
let apiCalls = 0;

/**
 * 格式化耗时显示
 * @param {number} ms 毫秒数
 * @returns {string} 格式化的时间字符串
 */
function formatTime(ms) {
  if (ms < 1000) {
    return `${ms}ms`;
  } else {
    return `${(ms / 1000).toFixed(2)}s`;
  }
}

/**
 * 验证经纬度是否合理
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @returns {object} 验证结果
 */
function validateCoordinates(latitude, longitude) {
  const issues = [];
  
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    issues.push('经纬度不是数字类型');
  }
  
  if (latitude < -90 || latitude > 90) {
    issues.push(`纬度超出范围: ${latitude} (应在-90到90之间)`);
  }
  
  if (longitude < -180 || longitude > 180) {
    issues.push(`经度超出范围: ${longitude} (应在-180到180之间)`);
  }
  
  // 检查是否是中国境内的坐标（大致范围）
  if (latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180) {
    if (latitude < 15 || latitude > 55 || longitude < 70 || longitude > 140) {
      if (latitude !== 30 || longitude !== 120) { // 排除默认fallback坐标
        issues.push('坐标可能不在中国境内');
      }
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues: issues
  };
}

/**
 * 测试单个地址
 * @param {string} address 地址
 * @param {string} category 分类
 * @returns {Promise<object>} 测试结果
 */
async function testSingleAddress(address, category) {
  const startTime = Date.now();
  totalTests++;
  
  try {
    console.log(`\n📍 测试地址: "${address}"`);
    
    const result = await geoService.getAddressLocation(address);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (!result) {
      failedTests++;
      console.log(`❌ 查询失败: 返回null或undefined`);
      return { success: false, address, category, error: '返回null' };
    }
    
    const { latitude, longitude } = result;
    const validation = validateCoordinates(latitude, longitude);
    
    // 判断是否来自缓存（耗时很短通常表示来自缓存）
    const isFromCache = duration < 100;
    if (isFromCache) {
      cacheHits++;
    } else {
      apiCalls++;
    }
    
    // 显示结果
    const cacheIndicator = isFromCache ? '🚀' : '🌐';
    const timeColor = duration < 100 ? '' : duration < 2000 ? '' : '';
    
    console.log(`✅ 查询成功: 纬度=${latitude}, 经度=${longitude}`);
    console.log(`⏱️ 耗时: ${formatTime(duration)} ${cacheIndicator} ${isFromCache ? '(缓存)' : '(API)'}`);
    
    // 显示验证结果
    if (!validation.isValid) {
      console.log(`⚠️ 坐标验证警告:`);
      validation.issues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }
    
    successfulTests++;
    return {
      success: true,
      address,
      category,
      latitude,
      longitude,
      duration,
      isFromCache,
      validation
    };
    
  } catch (error) {
    failedTests++;
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.error(`❌ 测试出错: ${error.message}`);
    console.log(`⏱️ 耗时: ${formatTime(duration)}`);
    
    return {
      success: false,
      address,
      category,
      error: error.message,
      duration
    };
  }
}

/**
 * 运行所有测试
 */
async function runComprehensiveTests() {
  console.log('🧪 ===== 地理位置服务综合测试开始 =====\n');
  
  const allResults = [];
  
  // 按分类运行测试
  for (const testCase of testCases) {
    console.log(`\n${testCase.category} 测试开始:`);
    console.log('='.repeat(50));
    
    for (const address of testCase.addresses) {
      const result = await testSingleAddress(address, testCase.category);
      allResults.push(result);
      
      // 在API调用之间添加小延迟，避免频率限制
      if (!result.isFromCache) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
  }
  
  // 显示测试总结
  console.log('\n📊 ===== 测试总结 =====');
  console.log(`总测试数: ${totalTests}`);
  console.log(`成功: ${successfulTests} ✅`);
  console.log(`失败: ${failedTests} ❌`);
  console.log(`成功率: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
  console.log(`缓存命中: ${cacheHits} 🚀`);
  console.log(`API调用: ${apiCalls} 🌐`);
  console.log(`缓存命中率: ${totalTests > 0 ? ((cacheHits / totalTests) * 100).toFixed(1) : 0}%`);
  
  // 显示失败的测试
  const failedResults = allResults.filter(r => !r.success);
  if (failedResults.length > 0) {
    console.log('\n❌ 失败的测试:');
    failedResults.forEach(result => {
      console.log(`   - "${result.address}": ${result.error}`);
    });
  }
  
  // 显示有问题的坐标
  const problematicResults = allResults.filter(r => r.success && r.validation && !r.validation.isValid);
  if (problematicResults.length > 0) {
    console.log('\n⚠️ 坐标验证警告:');
    problematicResults.forEach(result => {
      console.log(`   - "${result.address}": ${result.validation.issues.join(', ')}`);
    });
  }
  
  console.log('\n🎉 ===== 测试完成 =====');
}

// 执行测试
runComprehensiveTests().catch(error => {
  console.error('测试过程中发生严重错误:', error);
  process.exit(1);
});
