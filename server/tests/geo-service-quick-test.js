/**
 * 地理位置服务快速测试
 * 用于快速验证服务是否正常工作
 */

const geoService = require('../services/geo-service');

// 快速测试地址列表（包含已知和未知地址）
const quickTestAddresses = [
  '北京市海淀区',      // 应该能找到
  '上海市静安区',      // 可能需要API查询
  '广州市天河区',      // 应该能找到
  '深圳市南山区',      // 应该能找到
  '成都市武侯区',      // 应该能找到
  '杭州市余杭区',      // 可能需要API查询
  '南京市鼓楼区',      // 可能需要API查询
  '武汉市洪山区',      // 可能需要API查询
  '西安市雁塔区',      // 可能需要API查询
  '不存在的地址XYZ'    // 应该返回默认坐标
];

/**
 * 快速测试单个地址
 * @param {string} address 地址
 * @param {number} index 索引
 */
async function quickTestAddress(address, index) {
  const startTime = Date.now();
  
  try {
    console.log(`${index + 1}. 测试: "${address}"`);
    
    const result = await geoService.getAddressLocation(address);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (result) {
      const { latitude, longitude } = result;
      const isFromCache = duration < 100;
      const source = isFromCache ? '缓存' : 'API';
      const emoji = isFromCache ? '🚀' : '🌐';
      
      console.log(`   ✅ 成功: (${latitude}, ${longitude}) - ${duration}ms ${emoji} ${source}`);
      
      // 检查是否是默认坐标
      if (latitude === 30 && longitude === 120) {
        console.log(`   ⚠️ 返回默认坐标 (可能是无效地址)`);
      }
    } else {
      console.log(`   ❌ 失败: 返回null`);
    }
    
  } catch (error) {
    console.log(`   ❌ 错误: ${error.message}`);
  }
}

/**
 * 运行快速测试
 */
async function runQuickTest() {
  console.log('⚡ 地理位置服务快速测试');
  console.log('=' .repeat(40));
  
  const startTime = Date.now();
  
  // 顺序测试所有地址
  for (let i = 0; i < quickTestAddresses.length; i++) {
    await quickTestAddress(quickTestAddresses[i], i);
    
    // 在测试之间添加小延迟
    if (i < quickTestAddresses.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  const totalTime = Date.now() - startTime;
  
  console.log('=' .repeat(40));
  console.log(`✅ 快速测试完成 - 总耗时: ${totalTime}ms`);
  console.log(`📊 平均每个地址: ${Math.round(totalTime / quickTestAddresses.length)}ms`);
}

// 如果直接运行此文件，则执行快速测试
if (require.main === module) {
  runQuickTest().catch(error => {
    console.error('快速测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runQuickTest,
  quickTestAddresses
};
