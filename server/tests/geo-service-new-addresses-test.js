/**
 * 地理位置服务新地址测试
 * 专门测试本地缓存中不存在的新地址
 */

const geoService = require('../services/geo-service');

// 新地址测试列表（这些地址应该不在本地缓存中）
const newAddresses = [
  // 一线城市的新区域
  '北京市大兴区',
  '上海市青浦区', 
  '广州市南沙区',
  '深圳市光明区',
  
  // 二线城市
  '天津市滨海新区',
  '重庆市渝北区',
  '苏州市吴江区',
  '无锡市滨湖区',
  
  // 三线城市
  '珠海市香洲区',
  '中山市火炬开发区',
  '佛山市顺德区',
  '东莞市松山湖',
  
  // 县级市
  '江苏省张家港市',
  '浙江省义乌市',
  '山东省荣成市',
  '福建省石狮市',
  
  // 特殊地区
  '新疆维吾尔自治区克拉玛依市',
  '西藏自治区日喀则市',
  '内蒙古自治区包头市',
  '宁夏回族自治区石嘴山市'
];

/**
 * 测试新地址
 * @param {string} address 地址
 * @param {number} index 索引
 */
async function testNewAddress(address, index) {
  console.log(`\n${index + 1}. 🆕 测试新地址: "${address}"`);
  
  const startTime = Date.now();
  
  try {
    const result = await geoService.getAddressLocation(address);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (result) {
      const { latitude, longitude } = result;
      
      // 判断结果类型
      let resultType = '';
      let emoji = '';
      
      if (duration < 100) {
        resultType = '本地缓存';
        emoji = '🚀';
      } else if (latitude === 30 && longitude === 120) {
        resultType = '默认坐标 (API查询失败)';
        emoji = '⚠️';
      } else {
        resultType = 'API查询成功';
        emoji = '🌐';
      }
      
      console.log(`   ✅ 结果: 纬度=${latitude}, 经度=${longitude}`);
      console.log(`   ⏱️ 耗时: ${duration}ms`);
      console.log(`   📍 来源: ${resultType} ${emoji}`);
      
      // 验证坐标合理性
      if (latitude >= 15 && latitude <= 55 && longitude >= 70 && longitude <= 140) {
        console.log(`   ✅ 坐标验证: 在中国境内范围`);
      } else if (latitude === 30 && longitude === 120) {
        console.log(`   ⚠️ 坐标验证: 默认fallback坐标`);
      } else {
        console.log(`   ⚠️ 坐标验证: 可能超出中国境内范围`);
      }
      
    } else {
      console.log(`   ❌ 查询失败: 返回null`);
    }
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log(`   ❌ 查询异常: ${error.message}`);
    console.log(`   ⏱️ 耗时: ${duration}ms`);
  }
}

/**
 * 运行新地址测试
 */
async function runNewAddressTest() {
  console.log('🆕 ===== 新地址测试开始 =====');
  console.log('测试目标: 验证API调用和缓存机制');
  console.log('预期: 第一次查询调用API，第二次查询使用缓存');
  console.log('=' .repeat(50));
  
  let apiCalls = 0;
  let cacheHits = 0;
  let failures = 0;
  
  const startTime = Date.now();
  
  // 测试所有新地址
  for (let i = 0; i < newAddresses.length; i++) {
    const testStartTime = Date.now();
    
    await testNewAddress(newAddresses[i], i);
    
    const testDuration = Date.now() - testStartTime;
    
    // 统计
    if (testDuration < 100) {
      cacheHits++;
    } else {
      apiCalls++;
    }
    
    // 在API调用之间添加延迟，避免频率限制
    if (testDuration >= 100 && i < newAddresses.length - 1) {
      console.log('   ⏳ 等待300ms避免API频率限制...');
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  }
  
  const totalTime = Date.now() - startTime;
  
  // 显示统计信息
  console.log('\n📊 ===== 测试统计 =====');
  console.log(`总测试地址: ${newAddresses.length}`);
  console.log(`API调用次数: ${apiCalls} 🌐`);
  console.log(`缓存命中次数: ${cacheHits} 🚀`);
  console.log(`失败次数: ${failures} ❌`);
  console.log(`总耗时: ${(totalTime / 1000).toFixed(2)}秒`);
  console.log(`平均每个地址: ${Math.round(totalTime / newAddresses.length)}ms`);
  
  if (apiCalls > 0) {
    console.log(`\n💡 提示: 现在再次运行测试，这些地址应该从缓存中快速返回`);
  }
  
  console.log('\n🎉 ===== 新地址测试完成 =====');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runNewAddressTest().catch(error => {
    console.error('新地址测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runNewAddressTest,
  newAddresses
};
