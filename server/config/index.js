require('dotenv').config();

module.exports = {
  port: process.env.PORT || 3000,
  tencentMapKey: process.env.TENCENT_MAP_KEY,
  deepseek: {
    apiKey: process.env.DEEPSEEK_API_KEY,
    baseURL: 'https://api.deepseek.com/v1',
    timeout: 300000
  },
  security: {
    rateLimit: process.env.API_RATE_LIMIT || 100,
    allowedOrigins: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : []
  },
  logging: {
    // 根据环境自动调整日志级别
    level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'warn' : 'debug'),
    // 日志保留天数
    retentionDays: process.env.LOG_RETENTION_DAYS || 14,
    // 单个日志文件最大大小
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    // 是否启用日志压缩
    compress: process.env.LOG_COMPRESS !== 'false',
    // 敏感字段列表
    sensitiveFields: [
      'password', 'token', 'apiKey', 'key', 'secret',
      'DEEPSEEK_API_KEY', 'TENCENT_MAP_KEY'
    ]
  }
};