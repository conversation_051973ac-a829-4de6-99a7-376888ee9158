/**
 * Python八字计算接口
 * 用于调用Python实现的八字计算功能
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const logger = require('./logger');

// Python脚本路径
const PYTHON_SCRIPT_PATH = path.join(__dirname, '../bazi-py/bazi_api.py');
const TEMP_DIR = path.join(__dirname, '../temp');

/**
 * 调用Python八字计算
 * @param {Object} userInfo 用户信息 (已转换为真太阳时的时间)
 * @returns {Promise<Object>} 八字计算结果
 */
function calculateBaziWithPython(userInfo) {
  return new Promise((resolve, reject) => {
    try {
      const { year, month, day, hour, minute, gender, longitude } = userInfo;
      
      // 检查Python脚本是否存在
      if (!fs.existsSync(PYTHON_SCRIPT_PATH)) {
        throw new Error(`Python脚本不存在: ${PYTHON_SCRIPT_PATH}`);
      }
      
      // 设置默认经度（东八区标准经度）
      const longitudeValue = longitude ? parseFloat(longitude) : 120;
      
      // 准备输入数据 - 使用JSON格式与Python交互
      const inputData = {
        year: parseInt(year),
        month: parseInt(month),
        day: parseInt(day),
        hour: parseInt(hour),
        minute: parseInt(minute || 0),
        gender: gender === '男' ? 'male' : 'female',
        longitude: longitudeValue
      };
      
      logger.debug('【3. 调用Python】传递数据', { inputData });
      
      // 确保临时目录存在
      if (!fs.existsSync(TEMP_DIR)) {
        fs.mkdirSync(TEMP_DIR, { recursive: true });
      }
      
      // 创建临时文件路径
      const tempInputPath = path.join(TEMP_DIR, 'temp_input.json');
      const tempOutputPath = path.join(TEMP_DIR, 'temp_output.json');
      
      // 写入输入数据
      fs.writeFileSync(tempInputPath, JSON.stringify(inputData, null, 2), 'utf8');
      
      // 构建命令 - 调用Python脚本并传入参数
      const command = `python3 ${PYTHON_SCRIPT_PATH} --input ${tempInputPath} --output ${tempOutputPath}`;
      
      logger.debug(`【3. 调用Python】执行命令`, { command });
      
      // 执行Python脚本
      exec(command, (error, stdout, stderr) => {
        // 定义清理函数
        const cleanup = () => {
          try {
            if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
            if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
          } catch (cleanError) {
            logger.warn(`清理临时文件失败:`, { error: cleanError.message });
          }
        };
        
        if (error) {
          logger.error(`执行Python脚本错误:`, { error: error.message });
          cleanup();
          reject(new Error(`Python计算失败: ${error.message}`));
          return;
        }
        
        if (stderr) logger.warn(`Python脚本警告:`, { stderr });
        if (stdout) logger.debug(`Python脚本输出:`, { stdout });
        
        try {
          // 读取Python计算结果
          if (!fs.existsSync(tempOutputPath)) {
            throw new Error('Python计算未生成结果文件');
          }
          
          const resultJson = fs.readFileSync(tempOutputPath, 'utf8');
          logger.info(`八字计算完成，结果长度: ${resultJson.length}字符`);
          
          const result = JSON.parse(resultJson);
          cleanup();
          
          if (result.success) {
            resolve(result.data);
          } else {
            reject(new Error(`Python计算错误: ${result.error}`));
          }
        } catch (parseError) {
          cleanup();
          reject(new Error(`解析Python计算结果失败: ${parseError.message}`));
        }
      });
    } catch (error) {
      logger.error(`八字计算准备阶段失败:`, { error: error.message });
      reject(error);
    }
  });
}

module.exports = {
  calculateBaziWithPython
}; 