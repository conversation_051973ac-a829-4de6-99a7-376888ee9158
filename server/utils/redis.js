const Redis = require('ioredis');
const config = require('../config');
const logger = require('./logger');

class RedisClient {
  constructor() {
    this.client = config.redis.url ? new Redis(config.redis.url) : null;

    if (this.client) {
      this.client.on('error', (error) => {
        logger.error(`Redis Error: ${error.message}`);
      });

      this.client.on('connect', () => {
        logger.info('Redis connected successfully');
      });
    } else {
      logger.warn('Redis URL not configured, caching disabled');
    }
  }

  async get(key) {
    if (!this.client) return null;
    try {
      const value = await this.client.get(key);
      return value;
    } catch (error) {
      logger.error(`Redis get error: ${error.message}`);
      return null;
    }
  }

  async set(key, value, ttl) {
    if (!this.client) return;
    try {
      if (ttl) {
        await this.client.setex(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
    } catch (error) {
      logger.error(`Redis set error: ${error.message}`);
    }
  }
}

module.exports = new RedisClient(); 