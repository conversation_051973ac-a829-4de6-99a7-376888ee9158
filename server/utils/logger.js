/**
 * 简单日志系统 - 不依赖任何外部库
 * 直接使用fs.appendFileSync写入日志
 */

const fs = require('fs');
const path = require('path');
const config = require('../config/index');

// 确保日志目录存在
const logDir = path.join(__dirname, '..', 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// 获取当前日期，用于日志文件名
function getDateString() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 获取当前时间，用于日志条目
function getTimeString() {
  const now = new Date();
  // 使用本地时间格式
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const ms = String(now.getMilliseconds()).padStart(3, '0');
  
  // 获取时区信息
  const offset = -now.getTimezoneOffset();
  const offsetHours = Math.abs(Math.floor(offset / 60)).toString().padStart(2, '0');
  const offsetMinutes = (Math.abs(offset) % 60).toString().padStart(2, '0');
  const offsetSign = offset >= 0 ? '+' : '-';
  
  // 格式：YYYY-MM-DDThh:mm:ss.sss+hhmm (ISO 8601 带本地时区)
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${ms}${offsetSign}${offsetHours}${offsetMinutes}`;
}

// 日志级别
const LOG_LEVELS = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR',
  HTTP: 'HTTP'
};

// 日志级别权重
const LOG_LEVEL_WEIGHTS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  HTTP: 1  // HTTP级别等同于INFO
};

// 当前日志级别 - 默认为DEBUG级别（记录所有内容）
let currentLogLevel = LOG_LEVELS.DEBUG; // 这个初始值会被initLoggerByEnv方法覆盖

// 环境预设配置
const ENV_PRESETS = {
  development: {
    level: LOG_LEVELS.DEBUG,
    description: '开发模式：记录所有级别的日志'
  },
  test: {
    level: LOG_LEVELS.INFO,
    description: '测试模式：记录INFO级别及以上的日志'
  },
  production: {
    level: LOG_LEVELS.WARN,
    description: '生产模式：仅记录WARN和ERROR级别的日志'
  }
};

// 获取或设置当前日志级别
function getLogLevel() {
  return currentLogLevel;
}

function setLogLevel(level) {
  if (LOG_LEVELS[level]) {
    currentLogLevel = level;
    return true;
  } else {
    console.error(`无效的日志级别: ${level}，可选值: ${Object.keys(LOG_LEVELS).join(', ')}`);
    return false;
  }
}

// 检查是否应该记录此级别的日志
function shouldLog(level) {
  return LOG_LEVEL_WEIGHTS[level] >= LOG_LEVEL_WEIGHTS[currentLogLevel];
}

// 根据环境初始化日志系统
function initLoggerByEnv(customLevel) {
  const nodeEnv = process.env.NODE_ENV || 'development';
  const preset = ENV_PRESETS[nodeEnv] || ENV_PRESETS.development;
  
  // 自定义级别优先，其次使用环境预设
  let logLevel = preset.level;
  
  // 如果提供了自定义级别且有效，则使用自定义级别
  if (customLevel && LOG_LEVELS[customLevel.toUpperCase()]) {
    logLevel = customLevel.toUpperCase();
  }
  
  // 设置日志级别
  setLogLevel(logLevel);
  
  // 输出初始化信息
  const originalConsoleLog = console.log;
  originalConsoleLog(`当前环境: ${nodeEnv}`);
  originalConsoleLog(`日志级别: ${logLevel} (${ENV_PRESETS[nodeEnv]?.description || '自定义配置'})`);
  
  return { env: nodeEnv, level: logLevel };
}

// 清理旧日志文件
function cleanupOldLogs(retentionDays = config.logging.retentionDays || 14) {
  try {
    // 获取所有日志文件
    const files = fs.readdirSync(logDir);
    
    // 当前时间
    const now = new Date();
    
    // 保留天数的毫秒数
    const retentionMs = retentionDays * 24 * 60 * 60 * 1000;
    
    // 计算截止日期
    const cutoffDate = new Date(now.getTime() - retentionMs);
    
    let cleanedCount = 0;
    
    // 遍历文件
    for (const file of files) {
      try {
        // 解析文件名中的日期部分 (格式: YYYY-MM-DD-*.log)
        const dateMatch = file.match(/^(\d{4}-\d{2}-\d{2})-.*\.log$/);
        if (!dateMatch) continue;
        
        const fileDateStr = dateMatch[1];
        const fileDate = new Date(fileDateStr);
        
        // 如果文件日期早于截止日期，则删除
        if (fileDate < cutoffDate) {
          fs.unlinkSync(path.join(logDir, file));
          cleanedCount++;
        }
      } catch (err) {
        console.error(`清理日志文件 ${file} 失败: ${err.message}`);
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`已清理 ${cleanedCount} 个超过 ${retentionDays} 天的旧日志文件`);
    }
    
    return cleanedCount;
  } catch (err) {
    console.error(`清理旧日志文件失败: ${err.message}`);
    return 0;
  }
}

// 写入日志的核心函数
function writeLog(message, level = LOG_LEVELS.INFO, meta = {}) {
  try {
    // 首先检查是否应该记录此级别的日志
    if (!shouldLog(level)) {
      return false;
    }
    
    const dateStr = getDateString();
    const timeStr = getTimeString();
    
    // 根据日志级别确定写入的文件
    let logFiles = [];
    
    // 通用日志文件
    const combinedLogFile = path.join(logDir, `${dateStr}-combined.log`);
    logFiles.push(combinedLogFile);
    
    // 错误日志单独存储
    if (level === LOG_LEVELS.ERROR) {
      const errorLogFile = path.join(logDir, `${dateStr}-error.log`);
      logFiles.push(errorLogFile);
    }
    
    // HTTP日志单独存储
    if (level === LOG_LEVELS.HTTP) {
      const httpLogFile = path.join(logDir, `${dateStr}-http.log`);
      logFiles.push(httpLogFile);
    }
    
    // 处理对象类型的消息
    const formattedMessage = typeof message === 'object' 
      ? JSON.stringify(message) 
      : String(message);
    
    // 处理元数据
    const metaStr = Object.keys(meta).length > 0 
      ? ' ' + JSON.stringify(meta) 
      : '';
    
    // 构建日志条目
    const logEntry = `${timeStr} [${level}]: ${formattedMessage}${metaStr}\n`;
    
    // 写入所有相关日志文件
    for (const file of logFiles) {
      try {
        fs.appendFileSync(file, logEntry);
      } catch (err) {
        console.error(`写入日志文件 ${file} 失败: ${err.message}`);
      }
    }
    
    // 同时写入直接日志文件（便于调试）
    const directLogFile = path.join(logDir, `${dateStr}-direct.log`);
    try {
      fs.appendFileSync(directLogFile, logEntry);
    } catch (err) {
      // 忽略直接日志写入错误
    }
    
    return true;
  } catch (err) {
    console.error(`写入日志失败: ${err.message}`);
    return false;
  }
}

// HTTP请求日志中间件
function httpLoggerMiddleware(req, res, next) {
  const startTime = Date.now();
  
  // 在响应完成时记录日志
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logData = {
      method: req.method,
      url: req.originalUrl || req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress,
      userAgent: req.headers['user-agent']
    };
    
    writeLog(`HTTP ${req.method} ${logData.url} ${res.statusCode} ${duration}ms`, LOG_LEVELS.HTTP, logData);
  });
  
  next();
}

// 导出日志函数
const logger = {
  debug: (message, meta = {}) => writeLog(message, LOG_LEVELS.DEBUG, meta),
  info: (message, meta = {}) => writeLog(message, LOG_LEVELS.INFO, meta),
  warn: (message, meta = {}) => writeLog(message, LOG_LEVELS.WARN, meta),
  error: (message, meta = {}) => writeLog(message, LOG_LEVELS.ERROR, meta),
  http: (message, meta = {}) => writeLog(message, LOG_LEVELS.HTTP, meta),
  
  // 获取和设置日志级别
  getLogLevel,
  setLogLevel,
  
  // 环境初始化
  initLoggerByEnv,
  
  // HTTP日志中间件
  httpLoggerMiddleware: httpLoggerMiddleware,
  
  // 清理旧日志文件
  cleanupOldLogs: cleanupOldLogs,
  
  // 重写控制台方法
  overrideConsole: () => {
    const originalConsoleLog = console.log;
    const originalConsoleInfo = console.info;
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;
    
    console.log = function() {
      originalConsoleLog.apply(console, arguments);
      try {
        const args = Array.from(arguments).map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ');
        writeLog(args, LOG_LEVELS.INFO);
      } catch (err) {
        // 忽略错误
      }
    };
    
    console.info = function() {
      originalConsoleInfo.apply(console, arguments);
      try {
        const args = Array.from(arguments).map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ');
        writeLog(args, LOG_LEVELS.INFO);
      } catch (err) {
        // 忽略错误
      }
    };
    
    console.warn = function() {
      originalConsoleWarn.apply(console, arguments);
      try {
        const args = Array.from(arguments).map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ');
        writeLog(args, LOG_LEVELS.WARN);
      } catch (err) {
        // 忽略错误
      }
    };
    
    console.error = function() {
      originalConsoleError.apply(console, arguments);
      try {
        const args = Array.from(arguments).map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ');
        writeLog(args, LOG_LEVELS.ERROR);
      } catch (err) {
        // 忽略错误
      }
    };
    
    // 设置每天执行的任务
    setInterval(() => {
      try {
        const now = new Date();
        
        // 每天0点生成心跳日志
        if (now.getHours() === 0 && now.getMinutes() === 0) {
          const dateStr = getDateString();
          const testLogFile = path.join(logDir, `${dateStr}-heartbeat.log`);
          fs.appendFileSync(testLogFile, `每日心跳测试 ${getTimeString()} - 日志系统正常运行\n`);
          
          // 记录系统状态信息
          const info = {
            timestamp: getTimeString(),
            date: dateStr,
            logLevel: currentLogLevel,
            environment: process.env.NODE_ENV || 'development',
            nodeVersion: process.version,
            logDir: logDir
          };
          
          fs.appendFileSync(testLogFile, JSON.stringify(info, null, 2) + '\n');
          originalConsoleLog(`已生成每日心跳日志: ${testLogFile}`);
        }
        
        // 每天凌晨1点左右执行日志清理（保留配置中指定的天数）
        if (now.getHours() === 1 && now.getMinutes() < 10) {
          cleanupOldLogs(config.logging.retentionDays || 14);
        }
      } catch (err) {
        // 忽略错误
        originalConsoleError(`生成心跳日志或清理日志时出错: ${err.message}`);
      }
    }, 360000); // 每小时检查一次，但只在特定时间执行操作
    
    originalConsoleLog('控制台输出已重定向到日志文件系统');
  }
};

// 注意：这里不要调用logger方法，因为还没有初始化级别
// 环境相关的初始化将在app.js中完成

// 导出
module.exports = logger;