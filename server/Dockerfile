    # Use a base image that includes Node.js
    # node:16-buster is based on Debian Buster and usually includes Python 3.7
    FROM node:16-buster

    # Set the working directory in the container
    WORKDIR /app

    # Update package lists and install python3 and python3-pip
    # python3 is often pre-installed in buster, but explicit installation is harmless.
    # python3-pip is crucial for the pip3 command.
    RUN apt-get update && \
        apt-get install -y python3 python3-pip && \
        apt-get clean && \
        rm -rf /var/lib/apt/lists/*

    # Copy the Python requirements file first to leverage Docker cache
    COPY ./bazi-py/requirements.txt /tmp/requirements.txt

    # Install Python dependencies using a PyPI mirror
    # This example uses the Tsinghua University mirror.
    # You can replace this with another mirror if needed (e.g., one provided by Tencent Cloud).
    # --trusted-host is necessary if the mirror uses HTTP or has a certificate not in the default CA bundle.
    RUN pip3 install --no-cache-dir \
        -i https://pypi.tuna.tsinghua.edu.cn/simple \
        -r /tmp/requirements.txt

    # Copy Node.js package definition files
    COPY package*.json ./

    # Install Node.js dependencies (production only)
    RUN npm install --only=production

    # Copy the rest of your application code
    # Consider using a .dockerignore file to exclude unnecessary files/directories
    COPY . .

    # Expose the port your application will run on
    # This should match the port in your wxcloud.config.json and your app's listening port
    EXPOSE 80

    # Define the command to run your application
    CMD ["node", "app.js"]
    