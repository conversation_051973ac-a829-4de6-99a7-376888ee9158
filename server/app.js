const express = require('express');
const http = require('http');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const config = require('./config');
const apiRouter = require('./routes/api');
const setupNativeWebsocket = require('./services/native-websocket');

// 导入简单日志系统
const logger = require('./utils/logger');

// 根据环境初始化日志系统
const logConfig = logger.initLoggerByEnv(config.logging.level);
logger.info(`服务器日志系统已初始化: 环境=${logConfig.env}, 日志级别=${logConfig.level}, 心跳频率=每日一次`);

// 激活控制台重定向
logger.overrideConsole();

const app = express();
const server = http.createServer(app);

// 仅初始化原生WebSocket服务
const wss = setupNativeWebsocket(server);
logger.info('仅启用原生WebSocket服务，未启用Socket.io');

// 记录配置的允许域名
logger.info(`已配置CORS允许的域名: ${JSON.stringify(config.security.allowedOrigins)}`);

// 添加HTTP请求日志中间件（在所有其他中间件之前）
app.use(logger.httpLoggerMiddleware);

// 基础中间件
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: true, limit: '5mb' }));

// CORS配置
app.use(cors({
  origin: function(origin, callback) {
    // 如果未设置来源或来源在允许列表中
    const allowAll = config.security.allowedOrigins.length === 0;
    if (!origin || allowAll || config.security.allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('此来源因CORS策略而被拒绝访问'), false);
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true // 允许包含cookie
}));

// 设置更长的超时时间
app.use((req, res, next) => {
  // 增加超时时间以避免路由错误
  req.setTimeout(600000);  // 10分钟
  res.setTimeout(600000);  // 10分钟
  
  // 添加错误处理
  req.on('error', (err) => {
    logger.error('请求错误:', { error: err.message, stack: err.stack });
  });
  
  res.on('error', (err) => {
    logger.error('响应错误:', { error: err.message, stack: err.stack });
  });
  
  next();
});

// 限流配置
const limiter = rateLimit({
  windowMs: 60 * 1000,
  max: config.security.rateLimit,
  handler: (req, res) => {
    res.status(429).json({
      code: 429,
      message: '请求过于频繁，请稍后再试'
    });
  }
});
app.use(limiter);

// 路由
app.use('/api/v1', apiRouter);

// 错误处理中间件
app.use((err, req, res, next) => {
  logger.error(`服务器错误: ${err.message}`);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误'
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  logger.info(`服务器在端口 ${PORT} 上启动，仅支持原生WebSocket通信`);
  logger.info(`当前环境: ${process.env.NODE_ENV || 'development'}, 日志级别: ${logger.getLogLevel()}`);
});

// 导出应用和服务器实例
module.exports = { app, server, wss };