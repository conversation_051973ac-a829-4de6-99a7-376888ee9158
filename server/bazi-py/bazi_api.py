#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 八字计算API接口

import argparse
import json
import sys
import datetime
from lunar_python import Lunar, Solar

def calculate_bazi(input_data):
    """根据输入数据计算八字信息"""
    try:
        # 解析输入数据 - 真太阳时已经计算好了，直接使用传入的时间
        year = input_data['year']
        month = input_data['month'] 
        day = input_data['day']
        hour = input_data['hour']
        minute = input_data.get('minute', 0)
        gender = input_data['gender']  # 'male' 或 'female'
        
        # 创建农历对象 - 直接使用传入的时间（假设已经是真太阳时）
        solar = Solar.fromYmdHms(year, month, day, hour, minute, 0)
        lunar = Lunar.fromSolar(solar)
        
        # 获取八字信息
        eight_char = lunar.getEightChar()
        
        # 获取年柱
        year_gan = eight_char.getYearGan()
        year_zhi = eight_char.getYearZhi() 
        year_gan_zhi = f"{year_gan}{year_zhi}"
        
        # 获取月柱
        month_gan = eight_char.getMonthGan()
        month_zhi = eight_char.getMonthZhi()
        month_gan_zhi = f"{month_gan}{month_zhi}"
        
        # 获取日柱
        day_gan = eight_char.getDayGan() 
        day_zhi = eight_char.getDayZhi()
        day_gan_zhi = f"{day_gan}{day_zhi}"
        
        # 获取时柱
        time_gan = eight_char.getTimeGan()
        time_zhi = eight_char.getTimeZhi()
        time_gan_zhi = f"{time_gan}{time_zhi}"
        
        # 获取大运信息
        gender_num = 1 if gender == 'male' else 0
        yun = eight_char.getYun(gender_num)
        
        # 起运年龄
        start_year = yun.getStartYear()
        start_month = yun.getStartMonth()
        start_day = yun.getStartDay()
        
        # 大运表
        da_yun_list = []
        da_yun_array = yun.getDaYun()
        
        for i in range(min(8, len(da_yun_array))):
            da_yun = da_yun_array[i]
            da_yun_item = {
                'sequence': i + 1,
                'age': da_yun.getStartAge(),
                'endAge': da_yun.getStartAge() + 10,
                'ganZhi': da_yun.getGanZhi(),
                'startYear': da_yun.getStartYear()
            }
            da_yun_list.append(da_yun_item)
        
        # 近期流年
        liu_nian_list = []
        current_year = datetime.datetime.now().year
        
        for i in range(5):  # 计算5年的流年
            y = current_year + i
            # 使用当年6月1日作为参考点，确保已经过了农历新年
            # 农历新年最晚也不会超过公历6月，这样可以确保获取到的是当年的农历年干支
            ly = Solar.fromYmd(y, 6, 1).getLunar()
            liu_nian_list.append({
                'year': y,
                'ganZhi': ly.getYearInGanZhi()
            })
        
        # 构建结果
        result = {
            'yearColumn': year_gan_zhi,
            'monthColumn': month_gan_zhi,
            'dayColumn': day_gan_zhi,
            'timeColumn': time_gan_zhi,
            'fullBazi': f"{year_gan_zhi} {month_gan_zhi} {day_gan_zhi} {time_gan_zhi}",
            'dayun': {
                'startYear': start_year,
                'startMonth': start_month,
                'startDay': start_day,
                'list': da_yun_list
            },
            'liunian': liu_nian_list,
            'solar': solar.toFullString(),
            'lunar': lunar.toFullString()
        }
        
        return {
            'success': True,
            'data': result
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def main():
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='八字计算API')
    parser.add_argument('--input', type=str, help='输入JSON文件路径')
    parser.add_argument('--output', type=str, help='输出JSON文件路径')
    parser.add_argument('--json', action='store_true', help='使用JSON格式输出')
    parser.add_argument('--longitude', type=float, help='出生地点经度（必须提供，用于真太阳时矫正）')
    parser.add_argument('--latitude', type=float, help='出生地点纬度（可选）')
    
    args = parser.parse_args()
    
    # 读取输入
    if args.input:
        try:
            with open(args.input, 'r', encoding='utf-8') as f:
                input_data = json.load(f)
        except Exception as e:
            print(f"读取输入文件错误: {e}", file=sys.stderr)
            return 1
    else:
        # 测试数据
        input_data = {
            'year': 1994,
            'month': 3,
            'day': 11,
            'hour': 9,
            'minute': 0,
            'gender': 'male'
        }
    
    # 始终假定传入的时间为公历时间，转换为真太阳时
    # 优先使用命令行参数中的经度，如果没有则尝试从输入数据中获取
    longitude = args.longitude
    if longitude is None and 'longitude' in input_data:
        longitude = float(input_data['longitude'])
    
    if longitude is None:
        print("错误：必须提供 --longitude 参数或在输入数据中包含经度信息，用于真太阳时矫正", file=sys.stderr)
        return 1
    
    # 保存经纬度信息
    input_data['longitude'] = longitude
    if args.latitude is not None:
        input_data['latitude'] = args.latitude
    elif 'latitude' in input_data:
        input_data['latitude'] = float(input_data['latitude'])

    # 进行真太阳时矫正
    dt = datetime.datetime(input_data['year'], input_data['month'], input_data['day'], input_data['hour'], input_data.get('minute', 0))
    correction_minutes = (longitude - 120) * 4
    corrected_dt = dt + datetime.timedelta(minutes=correction_minutes)
    
    # 输出矫正信息
    print(f"原始时间: {dt.strftime('%Y-%m-%d %H:%M')}, 经度: {longitude}°", file=sys.stderr)
    print(f"修正后的真太阳时: {corrected_dt.strftime('%Y-%m-%d %H:%M')} (修正了 {correction_minutes:.2f} 分钟)", file=sys.stderr)
    
    # 更新输入数据中的时间为矫正后的时间
    input_data['hour'] = corrected_dt.hour
    input_data['minute'] = corrected_dt.minute

    # 计算八字
    result = calculate_bazi(input_data)
    
    # 输出结果
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"写入输出文件错误: {e}", file=sys.stderr)
            return 1
    else:
        # 打印到控制台
        print(json.dumps(result, ensure_ascii=False, indent=2))
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 