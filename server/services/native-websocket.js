const WebSocket = require('ws');
const streamDeepseek = require('./stream-deepseek');
const pythonBazi = require('../utils/python-bazi');
const requestLock = require('./request-lock');
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

/**
 * 原生 WebSocket 服务
 * 关键改动 (2025‑05‑03)：
 * a. 在 WebSocket.Server 构造中加入 handleProtocols，显式协商 'json' 子协议。
 * b. 恢复服务器端 ping / pong：定时向客户端发送 ws.ping()；
 * 若 1 个周期未收到 pong，则 terminate 连接。（Netflix pattern）
 * c. 保留现有 heartbeat 消息分支，以兼容旧版客户端。
 * 关键改动 (Refactoring based on user request):
 * d. 新增 activeClients 映射，维护 infoHash 到最新活跃 WebSocket 连接的映射。
 * e. 修改 sendToClient 以根据 infoHash 智能路由消息到最新的连接。
 * f. 在连接关闭时清理 activeClients 映射。
 * 关键改动 (Filtering empty chunks):
 * g. 在 streamDeepseek 回调中增加判断，过滤 content 为空或仅包含空白字符的 chunk，避免向前端发送无效消息。
 */

/**
 * 设置原生WebSocket服务
 * @param {Object} server HTTP服务器实例
 * @returns {Object} WebSocket.Server实例
 */

function setupNativeWebsocket(server) {
    // 新增：维护 infoHash 到最新活跃 WebSocket 连接的映射
    const activeClients = new Map(); // Map<infoHash, WebSocket>

    // 1️⃣ 初始化 WS 服务器，根路径 + 子协议协商
    const wss = new WebSocket.Server({
        server,
        path: '/',

        /**
         * 协商子协议：
         * 如果客户端提出 'json'，则返回 'json'；否则不选子协议。
         */
        handleProtocols: (protocols /*, request */) => {
            const protoList = Array.isArray(protocols) ? protocols : Array.from(protocols || []);
            // 客户端主动声明就协商 'json'；否则保持空子协议继续握手
            return protoList.includes('json') ? 'json' : undefined;
        },

        perMessageDeflate: false,
        clientTracking: true,
        verifyClient: () => true // 默认放行
    });

    logger.info('原生 WebSocket 服务器已创建，路径 "/"，支持子协议 json');

    /* ========================== WebSocket发送辅助函数 (已修改) ========================== */
    /**
     * 向客户端发送数据。
     * 会根据 payload.data.infoHash 查找最新的 WebSocket 连接进行发送。
     * @param {WebSocket} targetWs - 原始目标 WebSocket (作为 fallback 或无 infoHash 时使用)
     * @param {Object} payload - 要发送的数据对象
     */
    function sendToClient(targetWs, payload) {
        const infoHash = payload?.data?.infoHash;
        // 优先使用 activeClients 中为该 infoHash 记录的最新 ws 连接
        const ws = infoHash ? (activeClients.get(infoHash) || targetWs) : targetWs;

        if (ws && ws.readyState === WebSocket.OPEN) {
             try {
                ws.send(JSON.stringify(payload));
                // logger.trace -> logger.debug
                logger.debug(`Sent to ${ws === targetWs ? 'original' : 'mapped'} WS (${infoHash || 'no hash'}): ${JSON.stringify(payload).substring(0, 100)}...`);
             } catch (sendError) {
                 logger.error(`Failed to send message via WebSocket for infoHash=${infoHash}: ${sendError.message}`);
             }
        } else {
             logger.warn(`Cannot send message: WebSocket not open or invalid for infoHash=${infoHash}. Target state: ${ws?.readyState}, Original state: ${targetWs?.readyState}`);
        }
    }

    function sendError(ws, message, infoHash = null) {
        const payload = {
            code: 400,
            message: message,
            success: false,
            data: { infoHash }
        };
        sendToClient(ws, payload);
    }

    /* ==========================  连接处理与保活  ========================== */
    wss.on('connection', (ws, req) => {
        const clientId = req.headers['sec-websocket-key'] || `client_${Date.now()}`;
        const clientIP = req.socket.remoteAddress;
        logger.info(`WS 连接建立: id=${clientId}, ip=${clientIP}`);

        if (req.socket) {
            req.socket.setKeepAlive(true, 60000);
            req.socket.setTimeout(300000);
        }

        ws.isAlive = true;
        ws.on('pong', () => { ws.isAlive = true; });

        sendToClient(ws, {
            code: 200,
            message: 'WebSocket连接已成功建立',
            success: true,
            data: null
        });

        ws.on('message', async (message) => {
            let currentInfoHash = null;
            try {
                const rawMessage = message.toString();
                const receivedTime = new Date().toISOString();
                logger.debug(`【${receivedTime}】收到消息 ${rawMessage.length}B`);
                logger.debug(`内容: ${rawMessage}`);

                const data = JSON.parse(rawMessage);
                if (!data.type) {
                    sendError(ws, '消息缺少 type 字段');
                    return;
                }

                 currentInfoHash = data.data?.infoHash;
                 if (currentInfoHash) {
                    activeClients.set(currentInfoHash, ws);
                    ws.currentInfoHash = currentInfoHash;
                    logger.debug(`Registered/Updated active client mapping: ${currentInfoHash} -> WS ${clientId}`);
                 }

                switch (data.type) {
                    case 'startAnalysis':
                        await handleAnalysisRequest(ws, data.data || {});
                        break;
                    case 'requestSecondStage':
                        await handleSecondStageRequest(ws, data.data || {});
                        break;
                    case 'startChatAnalysis':
                        await handleChatAnalysisRequest(ws, data.data || {});
                        break;
                    case 'checkConnectionStatus':
                        sendToClient(ws, {
                            code: 200,
                            message: '连接正常',
                            success: true,
                            data: { connectionStatus: 'connected', timestamp: Date.now(), infoHash: currentInfoHash }
                        });
                        break;
                    case 'heartbeat':
                        const ts = data.data?.timestamp || Date.now();
                        logger.debug(`心跳 ⟶ id=${clientId} ts=${ts}`);
                        sendToClient(ws, {
                            code: 200,
                            message: '心跳响应',
                            success: true,
                            data: { type: 'heartbeatResponse', serverTime: Date.now(), clientTime: ts, infoHash: currentInfoHash }
                        });
                        break;
                    default:
                        sendError(ws, `未知请求类型: ${data.type}`, currentInfoHash);
                }
            } catch (err) {
                logger.error(`处理 WS 消息异常 id=${clientId}, infoHash=${currentInfoHash || 'N/A'}`, err);
                sendError(ws, `消息处理错误: ${err.message}`, currentInfoHash);
            }
        });

        ws.on('close', (code, reason) => {
            logger.info(`WS 连接关闭 id=${clientId}, code=${code}, reason=${reason?.toString()}`);
            const associatedInfoHash = ws.currentInfoHash;
            if (associatedInfoHash && activeClients.get(associatedInfoHash) === ws) {
                activeClients.delete(associatedInfoHash);
                logger.info(`清理了 infoHash=${associatedInfoHash} 的 activeClients 映射.`);
            } else if (associatedInfoHash) {
                 logger.debug(`WS关闭 id=${clientId}, 但 activeClients 中 ${associatedInfoHash} 已指向其他连接或不存在，无需清理.`);
            }
        });
        ws.on('error', (err) => {
             logger.error(`WS 错误 id=${clientId}, infoHash=${ws.currentInfoHash || 'N/A'}`, err);
        });
    });

    const pingInterval = 20000;
    const interval = setInterval(() => {
        wss.clients.forEach((ws) => {
            if (ws.isAlive === false) {
                const clientId = ws._socket?.remoteAddress || 'unknown';
                const infoHash = ws.currentInfoHash;
                logger.warn(`心跳超时 (ping timeout)，终止连接: id=${clientId}, infoHash=${infoHash || 'N/A'}`);
                 if (infoHash && activeClients.get(infoHash) === ws) {
                     activeClients.delete(infoHash);
                     logger.info(`清理了 infoHash=${infoHash} 的 activeClients 映射 (心跳超时).`);
                 }
                return ws.terminate();
            }
            ws.isAlive = false;
            try {
                ws.ping(() => {});
            } catch (pingError){
                 logger.warn(`发送 ping 失败: ${pingError.message}`);
                 const infoHash = ws.currentInfoHash;
                 if (infoHash && activeClients.get(infoHash) === ws) {
                     activeClients.delete(infoHash);
                 }
                 try { ws.terminate(); } catch {}
            }
        });
    }, pingInterval);

    wss.on('close', () => {
        clearInterval(interval);
        activeClients.clear();
        logger.info('WebSocket 服务器已关闭，清理了所有 activeClients 映射');
    });

  // 处理第一阶段推理分析请求
  async function handleAnalysisRequest(ws, userData) {
    const infoHash = userData.infoHash;
    logger.info(`收到原生WebSocket分析请求 [infoHash: ${infoHash}]`);

    const requiredFields = ['year', 'month', 'day', 'hour', 'gender'];
    const missingFields = requiredFields.filter(field => !userData[field]);

    if (missingFields.length > 0) {
      logger.warn(`请求缺少必要参数: ${missingFields.join(', ')} [infoHash: ${infoHash}]`);
      sendError(ws, `缺少必要参数: ${missingFields.join(', ')}`, infoHash);
      return;
    }

    if (infoHash && requestLock.isLocked(infoHash)) {
      logger.info(`infoHash=${infoHash}的请求已在处理中，等待结果 [infoHash: ${infoHash}]`);
      sendToClient(ws, {
        code: 202,
        message: "分析请求正在处理中，请稍候",
        success: true,
        data: { infoHash: infoHash, status: "processing" }
      });
      return;
    }

    if (infoHash) {
      const tempDir = path.join(process.cwd(), 'temp');
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);

      if (fs.existsSync(resultFile)) {
        try {
          const fileContent = fs.readFileSync(resultFile, 'utf8');
          const result = JSON.parse(fileContent);

          if (result.success && result.firstStage) {
            logger.info(`发现已有分析结果，直接返回 [infoHash: ${infoHash}]`);
            sendToClient(ws, {
              code: 200,
              type: 'response', // ADDED type:'response'
              message: "使用已有分析结果",
              success: true,
              data: {
                infoHash: infoHash,
                firstStage: result.firstStage,
                secondStage: result.secondStage || null
              }
            });
            return;
          }
        } catch (error) {
          logger.warn(`读取临时文件失败: ${error.message}`, { infoHash });
        }
      }
    }

    if (infoHash && !requestLock.tryLock(infoHash)) {
      logger.info(`无法锁定infoHash=${infoHash}的请求，可能已被其他请求锁定 [infoHash: ${infoHash}]`);
      sendToClient(ws, {
        code: 202,
        message: "分析请求正在处理中，请稍候",
        success: true,
        data: { infoHash: infoHash, status: "processing" }
      });
      return;
    }

    sendToClient(ws, {
      code: 102,
      type: 'analysisStatus',
      message: "开始分析...",
      success: true,
      data: { infoHash: infoHash, stage: "first", status: "started" }
    });

    try {
      let baziInfo;
      try {
        if (userData.longitude) {
          userData.longitude = parseFloat(userData.longitude);
        }
        baziInfo = await pythonBazi.calculateBaziWithPython(userData);
      } catch (error) {
        logger.error(`八字计算失败 [infoHash: ${infoHash}]`, { error: error.message });
        if (infoHash) requestLock.releaseLock(infoHash);
        sendError(ws, `八字计算失败: ${error.message}`, infoHash);
        return;
      }

      const analysisData = { ...userData, ...baziInfo };

      const analysisResult = await streamDeepseek.streamFirstStageAnalysis(
        analysisData,
        {},
        (chunk) => {
          if (!chunk || !chunk.content || chunk.content.trim() === "") {
            return;
          }
          sendToClient(ws, {
            code: 206,
            type: 'analysisChunk',
            message: "部分内容",
            success: true,
            data: {
              infoHash: infoHash,
              stage: 'first',
              type: chunk.type,
              content: chunk.content
            }
          });
        },
        null,
        false
      );

      if (analysisResult.success) {
        sendToClient(ws, {
          code: 200,
          type: 'analysisStatus',
          message: '推理分析完成',
          success: true,
          data: {
            infoHash: infoHash,
            stage: 'first',
            status: 'completed',
            paymentRequired: true
          }
        });

        sendToClient(ws, {
          code: 200,
          type: 'response', // ADDED type:'response'
          message: "推理分析完成",
          success: true,
          data: {
            infoHash: infoHash,
            firstStage: analysisResult.firstStage,
            paymentInfo: analysisResult.paymentInfo || {
              price: "19.9",
              currency: "CNY",
              description: "解锁高级八字分析，包含大运流年详解"
            }
          }
        });

      } else {
        logger.error(`分析失败 [infoHash: ${infoHash}]`, { error: analysisResult.error });
        sendError(ws, analysisResult.error || "分析过程失败", infoHash);
        if (infoHash && requestLock.isLocked(infoHash)) {
          requestLock.releaseLock(infoHash);
        }
      }

    } catch (error) {
      logger.error(`分析过程出错 [infoHash: ${infoHash}]`, { error: error.message });
      if (infoHash && requestLock.isLocked(infoHash)) {
        requestLock.releaseLock(infoHash);
      }
      sendError(ws, error.message || "分析过程出错", infoHash);
    }
  }

  // 处理第二阶段分析请求
  async function handleSecondStageRequest(ws, data) {
    const infoHash = data.infoHash;

    if (!infoHash) {
      sendError(ws, "缺少必要的infoHash参数");
      return;
    }

    logger.info(`收到第二阶段分析请求 [infoHash: ${infoHash}]`);

    let isPaid = true; // 临时模拟
    // ... (payment logic as before) ...

    if (!isPaid) {
        // ... (payment required message as before) ...
        return;
    }

    if (requestLock.isLocked(infoHash)) {
      logger.info(`infoHash=${infoHash}的请求已在处理中，等待结果 [infoHash: ${infoHash}]`);
      sendToClient(ws, {
        code: 202,
        message: "分析请求正在处理中，请稍候",
        success: true,
        data: { infoHash: infoHash, status: "processing" }
      });
      return;
    }

    const tempDir = path.join(process.cwd(), 'temp');
    const resultFile = path.join(tempDir, `${infoHash}.result.json`);

    if (!fs.existsSync(resultFile)) {
      sendError(ws, "未找到相关分析数据，请先完成基础分析", infoHash);
      return;
    }

    try {
      const fileContent = fs.readFileSync(resultFile, 'utf8');
      const result = JSON.parse(fileContent);

      if (result.secondStage) {
        logger.info(`发现已有第二阶段分析结果，直接返回 [infoHash: ${infoHash}]`);
        sendToClient(ws, {
          code: 200,
          type: 'response', // MAINTAINED type:'response'
          message: "使用已有分析结果",
          success: true,
          data: { infoHash: infoHash, secondStage: result.secondStage }
        });
        return;
      }

      if (!requestLock.tryLock(infoHash)) {
           logger.warn(`尝试锁定 infoHash=${infoHash} 失败，可能存在竞态条件`);
           sendToClient(ws, { code: 202, message: "请求冲突，请稍后重试", success: true, data: { infoHash: infoHash, status: "conflict" } });
           return;
      }

      if (result.firstStage && result.firstStage.content && result.basicPrompt) {
        sendToClient(ws, {
          code: 102,
          type: 'analysisStatus',
          message: "开始深度分析...",
          success: true,
          data: { infoHash: infoHash, stage: "second", status: "started" }
        });

        try {
          const analysisData = { infoHash: infoHash };
          const firstStageContent = result.firstStage.content;
          const basicPrompt = result.basicPrompt;

          const secondStageResult = await streamDeepseek.runSecondStageAnalysis(
            analysisData,
            basicPrompt,
            firstStageContent,
            {},
            (chunk) => {
              if (!chunk || !chunk.content || chunk.content.trim() === "") {
                return;
              }
              sendToClient(ws, {
                code: 206,
                type: 'analysisChunk',
                message: "部分内容",
                success: true,
                data: {
                  infoHash: infoHash,
                  stage: 'second',
                  type: chunk.type,
                  content: chunk.content
                }
              });
            },
            true
          );

          if (secondStageResult.success) {
            sendToClient(ws, {
              code: 200,
              type: 'analysisStatus',
              message: '深度分析完成',
              success: true,
              data: { infoHash: infoHash, stage: 'second', status: 'completed' }
            });

            sendToClient(ws, {
              code: 200,
              type: 'response', // MAINTAINED type:'response'
              message: "第二阶段分析完成",
              success: true,
              data: { infoHash: infoHash, secondStage: secondStageResult.secondStage }
            });

          } else {
            logger.error(`第二阶段分析失败 [infoHash: ${infoHash}]`, { error: secondStageResult.error });
             sendToClient(ws, {
                code: 500,
                message: secondStageResult.error || "第二阶段分析失败",
                success: false,
                data: {
                    infoHash: infoHash,
                    stage: 'second',
                    errorType: 'technical',
                    errorDetails: secondStageResult.errorDetails || '推理模型执行过程出现技术问题'
                }
            });
            if (requestLock.isLocked(infoHash)) {
              requestLock.releaseLock(infoHash);
            }
          }
        } catch (error) {
          logger.error(`第二阶段分析执行异常 [infoHash: ${infoHash}]`, { error: error.message });
          sendError(ws, "第二阶段分析异常: " + error.message, infoHash);
          if (requestLock.isLocked(infoHash)) {
            requestLock.releaseLock(infoHash);
          }
        }
      } else {
         if (requestLock.isLocked(infoHash)) {
             requestLock.releaseLock(infoHash);
         }
        sendToClient(ws, {
          code: 400,
          message: "缺少必要的分析数据，无法进行深度分析",
          success: false,
          data: {
            infoHash: infoHash,
            hasPrompt: !!result.basicPrompt,
            hasFirstStage: !!(result.firstStage && result.firstStage.content),
            message: "需要完整的第一阶段分析结果和基础提示词才能进行深度分析"
          }
        });
      }

    } catch (error) {
      logger.error(`读取或处理分析数据失败 [infoHash: ${infoHash}]`, { error: error.message });
      sendError(ws, "处理分析数据失败: " + error.message, infoHash);
      if (infoHash && requestLock.isLocked(infoHash)) {
        requestLock.releaseLock(infoHash);
      }
    }
  }

  // 处理Chat模型分析请求
  async function handleChatAnalysisRequest(ws, userData) {
    const infoHash = userData.infoHash;
    logger.info(`收到Chat模型分析请求 [infoHash: ${infoHash}]`);

    const requiredFields = ['year', 'month', 'day', 'hour', 'gender'];
    const missingFields = requiredFields.filter(field => !userData[field]);

    if (missingFields.length > 0) {
      logger.warn(`请求缺少必要参数: ${missingFields.join(', ')} [infoHash: ${infoHash}]`);
      sendError(ws, `缺少必要参数: ${missingFields.join(', ')}`, infoHash);
      return;
    }

    if (infoHash && requestLock.isLocked(infoHash)) {
      logger.info(`infoHash=${infoHash}的请求已在处理中，等待结果 [infoHash: ${infoHash}]`);
      sendToClient(ws, {
        code: 40001,
        message: "分析请求正在处理中，请稍候",
        success: true,
        data: {
          infoHash: infoHash,
          status: "processing",
          content: "您的分析请求正在处理中，请稍后刷新查看结果"
        }
      });
      return;
    }

    if (infoHash) {
      const tempDir = path.join(process.cwd(), 'temp');
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);

      if (fs.existsSync(resultFile)) {
        try {
          const fileContent = fs.readFileSync(resultFile, 'utf8');
          const result = JSON.parse(fileContent);

          if (result.success && result.content) {
            logger.info(`发现已有Chat分析结果，直接返回 [infoHash: ${infoHash}]`);
            sendToClient(ws, {
              code: 200,
              type: 'response', // MODIFIED from 'analysisResult' to 'response'
              message: "使用已有Chat分析结果",
              success: true,
              data: {
                infoHash: infoHash,
                content: result.content
              }
            });
            return;
          }
        } catch (error) {
          logger.warn(`读取临时文件失败: ${error.message}`, { infoHash });
        }
      }
    }

    if (infoHash && !requestLock.tryLock(infoHash)) {
      logger.info(`无法锁定infoHash=${infoHash}的请求，可能已被其他请求锁定 [infoHash: ${infoHash}]`);
      sendToClient(ws, {
        code: 40001,
        message: "分析请求正在处理中，请稍候",
        success: true,
        data: {
          infoHash: infoHash,
          status: "processing",
          content: "您的分析请求正在处理中，请稍后刷新查看结果"
        }
      });
      return;
    }

    sendToClient(ws, {
      code: 102,
      type: 'analysisStatus',
      message: "开始Chat模型分析...",
      success: true,
      data: {
        infoHash: infoHash,
        stage: "chat",
        status: "started"
      }
    });

    try {

        let baziInfo;
      try {
        if (userData.longitude) {
          userData.longitude = parseFloat(userData.longitude);
        } else {
          logger.info(`警告: 请求中没有经度信息 [infoHash: ${infoHash}]，将使用默认值120`);
        }
        baziInfo = await pythonBazi.calculateBaziWithPython(userData);
      } catch (error) {
        logger.error(`八字计算失败 [infoHash: ${infoHash}]`, { error: error.message });
        if (infoHash) requestLock.releaseLock(infoHash);
        sendError(ws, `八字计算失败: ${error.message}`, infoHash);
        return;
      }

      const analysisData = { ...userData, ...baziInfo };

      const chunkCallback = (chunk) => {
        if (!chunk || !chunk.content || chunk.content.trim() === "") {
          return;
        }
        sendToClient(ws, {
          code: 206,
          type: 'analysisChunk',
          message: "部分内容",
          success: true,
          data: {
            infoHash: infoHash,
            stage: 'chat',
            type: chunk.type,
            content: chunk.content
          }
        });
      };

      const chatResult = await streamDeepseek.streamChatAnalysis(
        analysisData,
        {},
        chunkCallback
      );

      if (chatResult.success) {
        sendToClient(ws, {
          code: 200,
          type: 'analysisStatus',
          message: 'Chat模型分析完成',
          success: true,
          data: {
            infoHash: infoHash,
            stage: 'chat',
            status: 'completed'
          }
        });

        sendToClient(ws, {
          code: 200,
          type: 'response', // MODIFIED from 'analysisResult' to 'response'
          message: "Chat模型分析完成",
          success: true,
          data: {
            infoHash: infoHash,
            content: chatResult.analysis
          }
        });

      } else {
        logger.error(`Chat模型分析失败 [infoHash: ${infoHash}]`, { error: chatResult.error });
        sendError(ws, chatResult.error || "Chat模型分析过程失败", infoHash);
      }

      if (infoHash && requestLock.isLocked(infoHash)) {
        requestLock.releaseLock(infoHash);
      }

    } catch (error) {
      logger.error(`Chat模型分析过程出错 [infoHash: ${infoHash}]`, { error: error.message });
      if (infoHash && requestLock.isLocked(infoHash)) {
        requestLock.releaseLock(infoHash);
      }
      sendError(ws, error.message || "Chat模型分析过程出错", infoHash);
    }
  }

  return wss;
}

module.exports = setupNativeWebsocket;