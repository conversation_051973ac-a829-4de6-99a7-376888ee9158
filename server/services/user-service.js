/**
 * 用户服务模块
 * 处理用户注册、登录、信息管理等功能
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const bcrypt = require('bcrypt');
const logger = require('../utils/logger');
const geoService = require('./geo-service');

// 用户数据存储目录
const USER_DATA_DIR = path.join(__dirname, '../data/userInfo');

// 确保用户数据目录存在
async function ensureUserDataDir() {
  try {
    await fs.access(USER_DATA_DIR);
  } catch (error) {
    await fs.mkdir(USER_DATA_DIR, { recursive: true });
    logger.info('[UserService] 创建用户数据目录:', USER_DATA_DIR);
  }
}

/**
 * 生成用户唯一ID
 * @returns {string} 用户唯一ID
 */
function generateUserId() {
  const timestamp = Date.now().toString(36);
  const randomStr = crypto.randomBytes(6).toString('hex');
  return `user_${timestamp}_${randomStr}`;
}

/**
 * 生成密码哈希
 * @param {string} password 明文密码
 * @returns {Promise<string>} 密码哈希
 */
async function hashPassword(password) {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
}

/**
 * 验证密码
 * @param {string} password 明文密码
 * @param {string} hashedPassword 哈希密码
 * @returns {Promise<boolean>} 验证结果
 */
async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

/**
 * 验证邮箱格式
 * @param {string} email 邮箱地址
 * @returns {boolean} 验证结果
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 * @returns {boolean} 验证结果
 */
function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 检查用户是否已存在
 * @param {string} identifier 用户标识（邮箱或手机号）
 * @returns {Promise<object|null>} 用户信息或null
 */
async function findUserByIdentifier(identifier) {
  try {
    await ensureUserDataDir();
    const files = await fs.readdir(USER_DATA_DIR);

    for (const file of files) {
      if (file.endsWith('.js')) {
        const filePath = path.join(USER_DATA_DIR, file);
        const content = await fs.readFile(filePath, 'utf8');

        // 解析用户数据
        const userDataMatch = content.match(/module\.exports\s*=\s*({[\s\S]*});/);
        if (userDataMatch) {
          const userData = eval('(' + userDataMatch[1] + ')');
          if (userData.email === identifier || userData.phone === identifier) {
            return { ...userData, userId: file.replace('.js', '') };
          }
        }
      }
    }

    return null;
  } catch (error) {
    logger.error('[UserService] 查找用户失败:', error);
    return null;
  }
}

/**
 * 创建新用户
 * @param {object} userInfo 用户信息
 * @returns {Promise<object>} 创建结果
 */
async function createUser(userInfo) {
  try {
    await ensureUserDataDir();

    const {
      registerType,    // 注册类型：email, phone, wechat
      email,
      phone,
      password,
      // wechatOpenId, // Will be extracted from wechatUserInfo if registerType is 'wechat'
      // wechatUnionId, // Will be extracted from wechatUserInfo if registerType is 'wechat'
      wechatUserInfo, // Contains openId and unionId for WeChat registration
      // name, // Removed as per user request, backend will generate nickname
      gender,
      year,
      month,
      day,
      hour,
      minute,
      location, // Renamed from birthPlace for frontend consistency
      additionalInfo
    } = userInfo;

    // Construct birthDate, birthTime, and birthPlace from frontend fields
    const birthDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const birthTime = `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`;
    const birthPlace = location; // Use location as birthPlace

    // 验证必要字段
    if (!registerType || !year || !month || !day || !location) { // Updated validation
      logger.error('[UserService] createUser - Missing required user info:', { registerType, year, month, day, location });
      throw new Error('缺少必要的用户信息 (年/月/日/地点)');
    }

    // Extract wechatOpenId and wechatUnionId if registerType is 'wechat'
    let wechatOpenId, wechatUnionId;
    if (registerType === 'wechat' && userInfo.wechatUserInfo) {
      wechatOpenId = userInfo.wechatUserInfo.openId || userInfo.wechatUserInfo.openid; // Handle potential variations in key name
      wechatUnionId = userInfo.wechatUserInfo.unionId || userInfo.wechatUserInfo.unionid;
    } else {
      // For non-wechat or if wechatUserInfo is missing, try to get from top level (legacy or other types)
      wechatOpenId = userInfo.wechatOpenId;
      wechatUnionId = userInfo.wechatUnionId;
    }

    // 根据注册类型验证
    if (registerType === 'email') {
      if (!email || !validateEmail(email)) {
        throw new Error('邮箱格式不正确');
      }
      if (!password || password.length < 6) {
        throw new Error('密码长度至少6位');
      }
    } else if (registerType === 'phone') {
      if (!phone || !validatePhone(phone)) {
        throw new Error('手机号格式不正确');
      }
      if (!password || password.length < 6) {
        throw new Error('密码长度至少6位');
      }
    } else if (registerType === 'wechat') {
      if (!wechatOpenId) {
        throw new Error('微信授权信息不完整');
      }
    }

    // 检查用户是否已存在
    const identifier = email || phone || wechatOpenId;
    const existingUser = await findUserByIdentifier(identifier);
    if (existingUser) {
      throw new Error('用户已存在');
    }

    // 查询出生地经纬度
    logger.info(`[UserService] 查询出生地经纬度: ${birthPlace}`);
    const geoResult = await geoService.getAddressLocation(birthPlace);

    if (!geoResult) {
      logger.error(`[UserService] 出生地地理位置查询失败 for: ${birthPlace}`);
      throw new Error('出生地地理位置查询失败');
    }

    // 生成用户ID
    const userId = generateUserId();

    // 准备用户数据
    const userData = {
      userId,
      registerType,
      email: email || null,
      phone: phone || null,
      passwordHash: password ? await hashPassword(password) : null,
      wechatOpenId: wechatOpenId || null,
      wechatUnionId: wechatUnionId || null,
      name: name || `用户${userId.slice(-6)}`, // 如果没有name，使用默认名称
      gender: gender || null,
      birthDate, // Constructed from year, month, day
      birthTime: birthTime || null, // Constructed from hour, minute
      birthPlace, // Now derived from 'location'
      birthCoordinates: {
        latitude: geoResult.latitude,
        longitude: geoResult.longitude
      },
      additionalInfo: additionalInfo || {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'active'
    };

    // 保存用户数据到文件
    const userFilePath = path.join(USER_DATA_DIR, `${userId}.js`);
    const fileContent = `/**
 * 用户信息文件
 * 用户ID: ${userId}
 * 创建时间: ${userData.createdAt}
 */

module.exports = ${JSON.stringify(userData, null, 2)};`;

    await fs.writeFile(userFilePath, fileContent, 'utf8');

    logger.info(`[UserService] 用户创建成功: ${userId}`);

    // 返回用户信息（不包含密码哈希）
    const { passwordHash, ...safeUserData } = userData;
    return {
      success: true,
      user: safeUserData
    };

  } catch (error) {
    logger.error('[UserService] 创建用户失败:', error);
    throw error;
  }
}

/**
 * 用户登录
 * @param {object} loginInfo 登录信息
 * @returns {Promise<object>} 登录结果
 */
async function loginUser(loginInfo) {
  try {
    const { loginType, identifier, password, wechatCode } = loginInfo;

    if (loginType === 'wechat') {
      // 微信登录逻辑（暂时简化）
      if (!wechatCode) {
        throw new Error('微信授权码不能为空');
      }

      // TODO: 调用微信API获取openId
      // 这里暂时模拟
      const user = await findUserByIdentifier(wechatCode);
      if (!user) {
        throw new Error('用户不存在，请先注册');
      }

      const { passwordHash, ...safeUserData } = user;
      return {
        success: true,
        user: safeUserData
      };

    } else {
      // 邮箱/手机号登录
      if (!identifier || !password) {
        throw new Error('用户名和密码不能为空');
      }

      const user = await findUserByIdentifier(identifier);
      if (!user) {
        throw new Error('用户不存在');
      }

      if (!user.passwordHash) {
        throw new Error('该用户未设置密码，请使用其他登录方式');
      }

      const isPasswordValid = await verifyPassword(password, user.passwordHash);
      if (!isPasswordValid) {
        throw new Error('密码错误');
      }

      const { passwordHash, ...safeUserData } = user;
      return {
        success: true,
        user: safeUserData
      };
    }

  } catch (error) {
    logger.error('[UserService] 用户登录失败:', error);
    throw error;
  }
}

/**
 * 获取用户信息
 * @param {string} userId 用户ID
 * @returns {Promise<object|null>} 用户信息
 */
async function getUserById(userId) {
  try {
    const userFilePath = path.join(USER_DATA_DIR, `${userId}.js`);
    const content = await fs.readFile(userFilePath, 'utf8');

    const userDataMatch = content.match(/module\.exports\s*=\s*({[\s\S]*});/);
    if (userDataMatch) {
      const userData = eval('(' + userDataMatch[1] + ')');
      const { passwordHash, ...safeUserData } = userData;
      return safeUserData;
    }

    return null;
  } catch (error) {
    logger.error(`[UserService] 获取用户信息失败: ${userId}`, error);
    return null;
  }
}

module.exports = {
  createUser,
  loginUser,
  getUserById,
  findUserByIdentifier,
  validateEmail,
  validatePhone
};
