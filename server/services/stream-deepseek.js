const deepseek = require('./deepseek');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const requestLock = require('./request-lock');

// Attempt to use global fetch if available (Node 18+), otherwise fallback to node-fetch
// Ensure 'node-fetch' is a dependency in your project (e.g., npm install node-fetch)
// This line handles node-fetch v2 (direct require) and v3+ (via .default when required in CJS).
const fetch = global.fetch || (require('node-fetch').default || require('node-fetch'));


// 模型类型常量 - 与原服务保持一致
const MODEL_TYPES = {
  CHAT: 'deepseek-chat',
  REASONER: 'deepseek-reasoner'
};

/**
 * 流式分析服务
 * 支持Chat模型和推理模型的流式输出
 */
class StreamDeepseekService {
  constructor() {
    this.API_KEY = process.env.DEEPSEEK_API_KEY;
    this.API_URL = 'https://api.deepseek.com/v1/chat/completions';

    if (!this.API_KEY) {
      // Throwing an error on construction is often better for unrecoverable states
      logger.error('DEEPSEEK_API_KEY is not set in environment variables.');
      throw new Error('DEEPSEEK_API_KEY is not set in environment variables.');
    }
    if (!this.API_URL) {
      logger.error('API_URL is not set.'); // Should ideally be a constant or checked
      throw new Error('API_URL is not configured.');
    }
    if (typeof fetch !== 'function') {
        logger.error('Fetch API is not available. Ensure Node 18+ or node-fetch is installed and correctly imported/required.');
        throw new Error('Fetch API is not available. Please install node-fetch or use Node 18+.');
    }
  }

  /**
   * 流式版本的analyze函数 - 专注于流式输出
   * @param {string} prompt 提示词
   * @param {string} modelType 模型类型: 'deepseek-chat' 或 'deepseek-reasoner'
   * @param {Object} options 附加选项
   * @param {Function} chunkCallback 每个响应块的回调函数
   * @returns {Promise<Object>} 完整的响应内容
   */
  async analyze(prompt, modelType = MODEL_TYPES.CHAT, options = {}, chunkCallback) {

    try {
      const modelName = modelType === MODEL_TYPES.REASONER ? '推理' : 'Chat';
      logger.info(`【DeepSeek ${modelName}】开始流式分析`);

      const messages = [
        {
          role: "system",
          content: options.systemPrompt || (modelType === MODEL_TYPES.REASONER ?
            "逻辑严密，往命理格局的最高层次指引" :
            "你是一位专业的人生规划导师，尤其擅长各类东西方玄学，如八字紫薇斗数星座奇门遁甲等。请尽可能开发用户的天赋，引导其人生最大潜能的释放，言语尽可能客观有趣，不用太温和")
        },
        {
          role: "user",
          content: prompt
        }
      ];

      if (options.history && Array.isArray(options.history) && options.history.length > 0) {
        const historyToAdd = options.history.length > 20 ? options.history.slice(-20) : options.history;
        messages.splice(1, 0, ...historyToAdd); // Corrected spread syntax
        if (options.history.length > 20) {
          logger.debug(`History too long (${options.history.length} messages), truncated to last 20.`);
        }
      }

      if (!chunkCallback || typeof chunkCallback !== 'function') {
        throw new Error('流式分析需要提供回调函数来处理内容块');
      }

      // DeepSeek chat model official limit is 8k tokens. Default to 8k if not specified.
      const maxTokens = 8000;

      const response = await fetch(this.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.API_KEY}`
        },
        body: JSON.stringify({
          model: modelType,
          messages: messages,
          temperature: options.temperature || 0.1,
          max_tokens: maxTokens,
          stream: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} - ${errorText}`);
      }

      // ===== 读取 SSE 流 =====
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let doneFlag = false; // Flag to signal [DONE] message was received

      let result = {};
      if (modelType === MODEL_TYPES.REASONER) {
        result = { success: true, reasoningContent: "", content: "" };
      } else { // CHAT model
        result = { success: true, analysis: "" };
      }

      // ① 浏览器 / Undici Web-Stream 路径
      if (response.body && typeof response.body.getReader === "function") {
        const reader = response.body.getReader();
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          buffer += decoder.decode(value, { stream: true });
          /* ↓ 维持原有 while(buffer.includes("\n")) 解析块 ↓ */
          while (buffer.includes("\n")) {
            const lineEnd = buffer.indexOf("\n");
            let line = buffer.substring(0, lineEnd).trim();
            buffer = buffer.substring(lineEnd + 1);

            if (line.startsWith("data: ")) {
              line = line.substring(6);

              if (line === "[DONE]") {
                doneFlag = true; // Set flag to break outer loop
                break; // Break from inner while (buffer.includes("\n"))
              }

              try {
                const parsedLine = JSON.parse(line);
                if (parsedLine.choices && parsedLine.choices[0].delta) {
                  const delta = parsedLine.choices[0].delta;

                  if (modelType === MODEL_TYPES.REASONER) {
                    if (delta.reasoning_content) {
                      const cleanContent = delta.reasoning_content.replace(/\bnull\b/g, ""); // Use word boundary for null
                      if (cleanContent) {
                        result.reasoningContent += cleanContent;
                        logger.debug(`Stream REASONER (reasoning): ${cleanContent.substring(0,50)}...`);
                        if (cleanContent.trim() !== "") {
                          chunkCallback({ type: "reasoning", content: cleanContent });
                        }
                      }
                    }
                    if (delta.content) {
                      const cleanContent = delta.content.replace(/\bnull\b/g, ""); // Use word boundary for null
                      if (cleanContent) {
                        result.content += cleanContent;
                        logger.debug(`Stream REASONER (content): ${cleanContent.substring(0,50)}...`);
                        if (cleanContent.trim() !== "") {
                          chunkCallback({ type: "conclusion", content: cleanContent });
                        }
                      }
                    }
                  } else { // Chat model
                    if (delta.content) {
                      const cleanContent = delta.content.replace(/\bnull\b/g, ""); // Use word boundary for null
                      if (cleanContent) {
                        if (!result.analysis) result.analysis = "";
                        result.analysis += cleanContent;
                        logger.debug(`Stream CHAT (analysis/conclusion): ${cleanContent.substring(0,50)}...`);
                        if (cleanContent.trim() !== "") {
                          chunkCallback({ type: "conclusion", content: cleanContent }); // Unified to "conclusion"
                        }
                      }
                    }
                  }
                }
              } catch (e) {
                logger.error(`解析流式响应失败: ${e.message}`, { line: line.substring(0, 100) + (line.length > 100 ? '...' : '') });
              }
            }
          }
          if (doneFlag) break; // Break outer loop if [DONE] was processed
        }
      // ② Node.js Readable-Stream 路径（node-fetch、云托管等）
      } else {
        for await (const chunkBuf of response.body) {
          buffer += decoder.decode(chunkBuf, { stream: true });
          while (buffer.includes("\n")) {
            const lineEnd = buffer.indexOf("\n");
            let line = buffer.substring(0, lineEnd).trim();
            buffer = buffer.substring(lineEnd + 1);

            if (line.startsWith("data: ")) {
              line = line.substring(6);

              if (line === "[DONE]") {
                doneFlag = true; // Set flag to break outer loop
                break; // Break from inner while (buffer.includes("\n"))
              }

              try {
                const parsedLine = JSON.parse(line);
                if (parsedLine.choices && parsedLine.choices[0].delta) {
                  const delta = parsedLine.choices[0].delta;

                  if (modelType === MODEL_TYPES.REASONER) {
                    if (delta.reasoning_content) {
                      const cleanContent = delta.reasoning_content.replace(/\bnull\b/g, ""); // Use word boundary for null
                      if (cleanContent) {
                        result.reasoningContent += cleanContent;
                        logger.debug(`Stream REASONER (reasoning): ${cleanContent.substring(0,50)}...`);
                        if (cleanContent.trim() !== "") {
                          chunkCallback({ type: "reasoning", content: cleanContent });
                        }
                      }
                    }
                    if (delta.content) {
                      const cleanContent = delta.content.replace(/\bnull\b/g, ""); // Use word boundary for null
                      if (cleanContent) {
                        result.content += cleanContent;
                        logger.debug(`Stream REASONER (content): ${cleanContent.substring(0,50)}...`);
                        if (cleanContent.trim() !== "") {
                          chunkCallback({ type: "conclusion", content: cleanContent });
                        }
                      }
                    }
                  } else { // Chat model
                    if (delta.content) {
                      const cleanContent = delta.content.replace(/\bnull\b/g, ""); // Use word boundary for null
                      if (cleanContent) {
                        if (!result.analysis) result.analysis = "";
                        result.analysis += cleanContent;
                        logger.debug(`Stream CHAT (analysis/conclusion): ${cleanContent.substring(0,50)}...`);
                        if (cleanContent.trim() !== "") {
                          chunkCallback({ type: "conclusion", content: cleanContent }); // Unified to "conclusion"
                        }
                      }
                    }
                  }
                }
              } catch (e) {
                logger.error(`解析流式响应失败: ${e.message}`, { line: line.substring(0, 100) + (line.length > 100 ? '...' : '') });
              }
            }
          }
          if (doneFlag) break; // Break outer loop if [DONE] was processed
        }
      }


      try {
        if (modelType === MODEL_TYPES.REASONER) {
          logger.info(`【DeepSeek ${modelName}】流式分析完成 - reasoningContent长度: ${result.reasoningContent?.length || 0}字符, content长度: ${result.content?.length || 0}字符`);
        } else {
          logger.info(`【DeepSeek ${modelName}】流式分析完成 - analysis长度: ${result.analysis?.length || 0}字符`);
          if (!result.analysis) {
            result.analysis = "";
            logger.warn("Chat模型分析完成，但未接收到分析内容");
          }
        }
      } catch (logError) {
        logger.error(`处理分析结果或记录日志时出错: ${logError.message}`);
      }
      return result;
    } catch (error) {
      logger.error('【错误】流式分析失败:', { error: error.message, stack: error.stack });
      return { success: false, error: error.message || '服务暂时不可用，请稍后再试' };
    }
  }


  async streamFirstStageAnalysis(analysisData, options = {}, firstStageCallback, secondStageCallback = null, autoStartSecondStage = false) {
    try {
      if (!firstStageCallback || typeof firstStageCallback !== 'function') {
        throw new Error('必须提供第一阶段回调函数');
      }
      logger.info('开始流式第一阶段分析');
      const basicPrompt = await deepseek.generatePrompt(analysisData);
      const guidancePart = `请基于此人八字命盘，分析此人的天赋性格，体貌特征，最适合从事哪些行业以及适合的岗位，适合在哪里发展，事业的大小，身体健康状况，再着重分析大运大富大贵的可能性，如何发挥最好的天赋和潜能。`;
      const fullPrompt = `${basicPrompt}\n${guidancePart}`;

      const firstStageResult = await this.analyze(fullPrompt, MODEL_TYPES.REASONER, options, firstStageCallback);

      if (!firstStageResult.success) {
        logger.error('第一阶段分析失败:', { error: firstStageResult.error });
        if (analysisData.infoHash && requestLock) {
          logger.info(`第一阶段分析失败，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
          requestLock.releaseLock(analysisData.infoHash);
        }
        return { success: false, error: firstStageResult.error, stage: 'first' };
      }

      const result = {
        success: true,
        firstStage: {
          reasoningContent: firstStageResult.reasoningContent,
          content: firstStageResult.content
        },
        paymentRequired: true,
        paymentInfo: { price: "19.9", currency: "CNY", description: "解锁高级八字分析，包含大运流年详解" }
      };

      if (analysisData.infoHash) {
        await this.saveFirstStageResult(analysisData.infoHash, firstStageResult, basicPrompt);
      }

      if (analysisData.infoHash && requestLock) {
        logger.info(`第一阶段完成，等待用户触发第二阶段，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
        requestLock.releaseLock(analysisData.infoHash);
      }
      return result;
    } catch (error) {
      logger.error('流式第一阶段分析失败:', { error: error.message, stack: error.stack });
      if (analysisData && analysisData.infoHash && requestLock) {
        logger.info(`分析过程出错，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
        requestLock.releaseLock(analysisData.infoHash);
      }
      return { success: false, error: error.message || '分析过程发生错误' };
    }
  }


  async runSecondStageAnalysis(analysisData, basicPrompt = null, firstStageContent = null, options = {}, callback = null, hasVerifiedPayment = false) {
    try {
      if (!hasVerifiedPayment) {
        logger.info(`需要验证支付状态 [infoHash: ${analysisData.infoHash}]`);
        const paymentVerified = true; // Placeholder for actual payment verification
        if (!paymentVerified) {
          logger.warn(`支付验证失败，拒绝执行第二阶段分析 [infoHash: ${analysisData.infoHash}]`);
          return { success: false, error: "需要支付才能进行深度分析", paymentRequired: true };
        }
        logger.info(`支付验证通过，开始执行第二阶段分析 [infoHash: ${analysisData.infoHash}]`);
      }

      if (analysisData.infoHash && (!basicPrompt || !firstStageContent)) {
        try {
          const tempDir = path.join(process.cwd(), 'temp');
          const resultFile = path.join(tempDir, `${analysisData.infoHash}.result.json`);
          if (fs.existsSync(resultFile)) {
            const fileContent = fs.readFileSync(resultFile, 'utf8');
            const resultData = JSON.parse(fileContent);
            if (!basicPrompt && resultData.basicPrompt) basicPrompt = resultData.basicPrompt;
            if (!firstStageContent && resultData.firstStage && resultData.firstStage.content) firstStageContent = resultData.firstStage.content;
          } else {
            logger.warn(`未找到结果文件: ${resultFile} [infoHash: ${analysisData.infoHash}]`);
          }
        } catch (error) {
          logger.warn(`从结果文件读取信息失败: ${error.message}`);
        }
      }

      if (!basicPrompt) throw new Error('缺少基础提示词，无法执行第二阶段分析');
      if (!firstStageContent) throw new Error('缺少第一阶段分析结果，无法执行第二阶段分析');

      logger.info('开始执行第二阶段分析');
      const secondStagePrompt = `${basicPrompt}\n基于以下八字第一轮分析结果，进一步分析未来大运和流年的关键机遇与挑战，重点关注事业发展和财富量级，尽可能客观理性，不要过度解读：\n\n${firstStageContent}`;
      const secondStageResult = await this.analyze(secondStagePrompt, MODEL_TYPES.REASONER, options, callback);

      if (!secondStageResult.success) {
        logger.error('第二阶段分析失败:', { error: secondStageResult.error });
        if (analysisData.infoHash && requestLock) {
          logger.info(`第二阶段分析失败，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
          requestLock.releaseLock(analysisData.infoHash);
        }
        return { success: false, error: secondStageResult.error };
      }

      if (analysisData.infoHash) {
        await this.saveSecondStageResult(analysisData.infoHash, secondStageResult);
        if (requestLock) {
          logger.info(`第二阶段分析完成，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
          requestLock.releaseLock(analysisData.infoHash);
        }
      }
      return { success: true, secondStage: { reasoningContent: secondStageResult.reasoningContent, content: secondStageResult.content }};
    } catch (error) {
      logger.error('第二阶段分析执行失败:', { error: error.message, stack: error.stack });
      if (analysisData.infoHash && requestLock) {
        logger.info(`第二阶段分析异常，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
        requestLock.releaseLock(analysisData.infoHash);
      }
      return { success: false, error: error.message || '第二阶段分析过程发生错误' };
    }
  }


  async saveFirstStageResult(infoHash, result, basicPrompt) {

    try {
      const tempDir = path.join(process.cwd(), 'temp');
      if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);
      const updateData = {
        success: true, infoHash,
        firstStage: { reasoningContent: result.reasoningContent, content: result.content },
        basicPrompt, lastUpdateTime: Date.now()
      };
      let existingData = {};
      if (fs.existsSync(resultFile)) {
        try { existingData = JSON.parse(fs.readFileSync(resultFile, 'utf8')); }
        catch (e) { logger.warn(`解析现有临时文件失败: ${e.message}`); }
      }
      const mergedData = { ...existingData, ...updateData }; // Corrected spread syntax
      fs.writeFileSync(resultFile, JSON.stringify(mergedData, null, 2), 'utf8');
      logger.info(`第一阶段分析结果和基础提示词已保存到临时文件: ${resultFile}`);
    } catch (error) {
      logger.error(`保存第一阶段结果失败: ${error.message}`);
    }
  }


  async saveSecondStageResult(infoHash, result) {

    try {
      const tempDir = path.join(process.cwd(), 'temp');
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);
      let existingData = {};
      if (fs.existsSync(resultFile)) {
        try { existingData = JSON.parse(fs.readFileSync(resultFile, 'utf8')); }
        catch (e) { logger.warn(`解析现有临时文件失败（将被覆盖部分属性）: ${e.message}`); }
      }
      const updateData = {
        secondStage: { reasoningContent: result.reasoningContent, content: result.content },
        lastUpdateTime: Date.now(), success: true, infoHash
      };
      const mergedData = { ...existingData, ...updateData }; // Corrected spread syntax
      fs.writeFileSync(resultFile, JSON.stringify(mergedData, null, 2), 'utf8');
      logger.info(`第二阶段分析结果已更新到临时文件: ${resultFile}`);
    } catch (error) {
      logger.error(`保存第二阶段结果失败: ${error.message}`);
    }
  }


  async streamChatAnalysis(analysisData, options = {}, callback) {
    try {
      if (!callback || typeof callback !== 'function') {
        throw new Error('必须提供回调函数处理流式内容');
      }
      logger.info('开始流式Chat模型分析');
      const basicPrompt = await deepseek.generatePrompt(analysisData);
      const guidancePart = `请基于此人八字命盘，分析此人的天赋性格，体貌特征，最适合从事哪些行业以及适合的岗位，适合在哪里发展，事业的大小，身体健康状况，再着重分析大运大富大贵的可能性，如何发挥最好的天赋和潜能。`;
      const chatPrompt = `${basicPrompt}\n${guidancePart}`;

      const chatResult = await this.analyze(chatPrompt, MODEL_TYPES.CHAT, options, callback);

      if (!chatResult.success) {
        logger.error('Chat模型分析失败:', { error: chatResult.error });
        if (analysisData.infoHash && requestLock) {
          logger.info(`Chat模型分析失败，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
          requestLock.releaseLock(analysisData.infoHash);
        }
        return { success: false, error: chatResult.error, stage: 'chat' };
      }

      if (!chatResult.analysis) {
        logger.warn(`Chat模型未返回分析内容，使用空字符串 [infoHash: ${analysisData.infoHash}]`);
        chatResult.analysis = "";
      }
      // Max_tokens in analyze() should handle truncation. This is a fallback.
      let finalAnalysis = chatResult.analysis;
      if (finalAnalysis.length > (options.maxTokens || 8000)) { // Check against effective maxTokens
         logger.info(`Chat分析结果过长(${finalAnalysis.length}字符)，可能已被API截断或客户端二次截断 [infoHash: ${analysisData.infoHash}]`);
         // finalAnalysis = finalAnalysis.substring(0, (options.maxTokens || 8000)) + "...(内容过长已截断)"; // Optional: client-side hard truncate
      }
      const result = { success: true, analysis: finalAnalysis };

      if (analysisData.infoHash) {
        await this.saveChatAnalysisResult(analysisData.infoHash, finalAnalysis);
      }
      if (analysisData.infoHash && requestLock) {
        logger.info(`Chat模型分析完成，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
        requestLock.releaseLock(analysisData.infoHash);
      }
      return result;
    } catch (error) {
      logger.error('流式Chat模型分析失败:', { error: error.message, stack: error.stack });
      if (analysisData && analysisData.infoHash && requestLock) {
        logger.info(`Chat模型分析过程出错，释放请求锁 [infoHash: ${analysisData.infoHash}]`);
        requestLock.releaseLock(analysisData.infoHash);
      }
      return { success: false, error: error.message || 'Chat模型分析过程发生错误' };
    }
  }


  async saveChatAnalysisResult(infoHash, analysisContent) {
    logger.debug(`保存Chat模型分析结果 - 内容长度: ${analysisContent?.length || 0}字符`);
    try {
      const tempDir = path.join(process.cwd(), 'temp');
      if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);
      const updateData = {
        success: true, infoHash,
        content: analysisContent, // Unified field name to 'content'
        lastUpdateTime: Date.now()
      };
      let existingData = {};
      if (fs.existsSync(resultFile)) {
        try { existingData = JSON.parse(fs.readFileSync(resultFile, 'utf8')); }
        catch (e) { logger.warn(`解析现有临时文件失败: ${e.message}`); }
      }
      const mergedData = { ...existingData, ...updateData }; // Corrected spread syntax
      fs.writeFileSync(resultFile, JSON.stringify(mergedData, null, 2), 'utf8');
      logger.info(`Chat模型分析结果已保存到临时文件: ${resultFile} (字段: content)`);
    } catch (error) {
      logger.error(`保存Chat模型分析结果失败: ${error.message}`);
    }
  }

}

module.exports = new StreamDeepseekService();