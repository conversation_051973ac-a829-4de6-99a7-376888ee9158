/**
 * 请求锁定服务
 * 用于跟踪和管理正在处理中的请求，避免重复处理相同的请求
 */

const logger = require('../utils/logger');

// 保存当前正在处理的请求列表
const activeRequests = new Set();

// 锁定超时时间（15分钟），防止死锁
const LOCK_TIMEOUT = 15 * 60 * 1000;

/**
 * 尝试锁定一个请求
 * @param {string} requestId 请求唯一标识（如infoHash）
 * @returns {boolean} 是否成功锁定（true表示可以处理，false表示已有相同请求在处理中）
 */
function tryLock(requestId) {
  if (!requestId) return true; // 如果没有提供requestId，默认允许处理
  
  // 如果请求已经在处理中，则拒绝
  if (activeRequests.has(requestId)) {
    logger.debug(`请求已锁定，拒绝重复处理: ${requestId}`);
    return false;
  }
  
  // 锁定请求
  activeRequests.add(requestId);
  logger.debug(`锁定请求: ${requestId}，当前活跃请求数: ${activeRequests.size}`);
  
  // 设置超时自动释放，防止死锁
  setTimeout(() => {
    if (activeRequests.has(requestId)) {
      logger.warn(`请求锁定超时自动释放: ${requestId}`);
      activeRequests.delete(requestId);
    }
  }, LOCK_TIMEOUT);
  
  return true;
}

/**
 * 释放一个请求锁
 * @param {string} requestId 请求唯一标识
 */
function releaseLock(requestId) {
  if (!requestId) return;
  
  const wasLocked = activeRequests.has(requestId);
  activeRequests.delete(requestId);
  
  if (wasLocked) {
    logger.debug(`释放请求锁: ${requestId}，当前活跃请求数: ${activeRequests.size}`);
  } else {
    logger.debug(`尝试释放未锁定的请求: ${requestId}，可能已被自动释放或从未锁定`);
  }
}

/**
 * 获取当前活跃请求数量
 * @returns {number} 活跃请求数量
 */
function getActiveRequestCount() {
  return activeRequests.size;
}

/**
 * 检查请求是否已锁定
 * @param {string} requestId 请求唯一标识
 * @returns {boolean} 是否已锁定
 */
function isLocked(requestId) {
  if (!requestId) return false;
  return activeRequests.has(requestId);
}

module.exports = {
  tryLock,
  releaseLock,
  getActiveRequestCount,
  isLocked
};