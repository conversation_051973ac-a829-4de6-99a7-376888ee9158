const OpenAI = require('openai');
const config = require('../config');
const logger = require('../utils/logger');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 模型类型常量
const MODEL_TYPES = {
  CHAT: 'deepseek-chat',
  REASONER: 'deepseek-reasoner'
};

class DeepSeekService {
  constructor() {
    this.client = new OpenAI({
      baseURL: 'https://api.deepseek.com',
      apiKey: config.deepseek.apiKey,
      timeout: 300000
    });
  }

  /**
   * 通用模型调用方法 - 可以选择使用chat模型或推理模型
   * @param {string} prompt 提示词
   * @param {string} modelType 模型类型，默认为chat模型
   * @param {Object} options 额外选项
   * @returns {Promise<Object>} 分析结果
   */
  async analyze(prompt, modelType = MODEL_TYPES.CHAT, options = {}) {
    try {
      const modelName = modelType === MODEL_TYPES.REASONER ? '推理' : 'Chat';
      logger.info(`【DeepSeek ${modelName}】开始分析`);
      
      // 构建消息数组
      const messages = [
        {
          role: "system",
          content: options.systemPrompt || (modelType === MODEL_TYPES.REASONER ? 
            "逻辑严密，往命理格局的最高层次指引" : 
            "你是一位专业的人生规划导师，尤其擅长各类东西方玄学，如八字紫薇斗数星座奇门遁甲等。请尽可能开发用户的天赋，引导其人生最大潜能的释放，言语尽可能客观有趣，不用太温和")
        },
        {
          role: "user",
          content: prompt
        }
      ];
      
      // 添加历史消息（如果有）
      if (options.history && Array.isArray(options.history) && options.history.length > 0) {
        // 插入历史消息在系统消息之后，用户消息之前
        messages.splice(1, 0, ...options.history);
      }
      
      // 输出完整的prompt内容 - 为保护敏感信息，仅记录长度
      logger.debug(`用户提示词长度: ${prompt.length}字符`);
      
      // 创建请求
      const completion = await this.client.chat.completions.create({
        model: modelType,
        messages: messages,
        temperature: options.temperature || 0.1,
        max_tokens: options.maxTokens || 8000,
        timeout: options.timeout || 300000
      });
      
      logger.info(`【DeepSeek ${modelName}】分析完成`);
      
      // 处理不同模型的响应格式
      if (completion.choices && completion.choices[0]) {
        if (modelType === MODEL_TYPES.REASONER) {
          return {
            success: true,
            reasoningContent: completion.choices[0].message.reasoning_content || "",
            content: completion.choices[0].message.content || ""
          };
        } else {
          return {
            success: true,
            analysis: completion.choices[0].message.content
          };
        }
      } else {
        logger.error('【错误】API响应格式错误');
        throw new Error('API响应格式错误');
      }
    } catch (error) {
      logger.error('【错误】分析失败:', { error: error.message, stack: error.stack });
      return {
        success: false,
        error: error.message || '服务暂时不可用，请稍后再试'
      };
    }
  }

  /**
   * 使用chat模型进行分析（保留向后兼容）
   * @param {string} prompt 提示词
   * @returns {Promise<Object>} 分析结果
   */
  async makeRequest(prompt) {
    return this.analyze(prompt, MODEL_TYPES.CHAT);
  }

  async generatePrompt(userInfo) {
    const { year, month, day, hour, minute, gender } = userInfo;
    
    // 直接使用传入的八字信息，不再调用baziUtils
    // 提取八字信息
    const yearColumn = userInfo.yearColumn || '未知';
    const monthColumn = userInfo.monthColumn || '未知';
    const dayColumn = userInfo.dayColumn || '未知';
    const timeColumn = userInfo.timeColumn || '未知';
    const fullBazi = userInfo.fullBazi || `${yearColumn} ${monthColumn} ${dayColumn} ${timeColumn}`;
    
    // 检查是否有大运信息
    let dayunInfo = '无';
    if (userInfo.dayun) {
      // 构建大运信息
      dayunInfo = `起运${userInfo.dayun.startYear || 0}年${userInfo.dayun.startMonth || 0}月${userInfo.dayun.startDay || 0}天后，`;
      
      if (userInfo.dayun.list && userInfo.dayun.list.length > 0) {
        dayunInfo += userInfo.dayun.list
          .slice(0, 8)  // 显示8个大运
          .map(dy => `${dy.age}岁-${dy.endAge}岁：${dy.ganZhi}运`)
          .join('，');
      } else {
        dayunInfo += '无大运数据';
      }
    }
    
    // 检查是否有流年信息
    let liunianInfo = '无';
    if (userInfo.liunian && userInfo.liunian.length > 0) {
      liunianInfo = userInfo.liunian
        .slice(0, 5)  // 显示未来5年
        .map(ly => `${ly.year}年：${ly.ganZhi}`)
        .join('，');
    }
    
    // 生成基本信息部分
    const basicInfo = `性别${gender}，公历出生日期是${year}年${month}月${day}日${hour}时${minute || 0}分。`;
    
    // 生成八字部分
    const baziPart = `八字四柱为：${yearColumn}年柱 ${monthColumn}月柱 ${dayColumn}日柱 ${timeColumn}时柱。`;
    
    // 生成大运和流年部分
    const dayunPart = `大运：${dayunInfo}。`;
    const liunianPart = `近期流年：${liunianInfo}。`;
    
    // 组合完整提示词
    const fullPrompt = `${basicInfo}\n${baziPart}\n${dayunPart}\n${liunianPart}`;
    
    return fullPrompt;
  }

  handleAPIError(error) {
    logger.error('【错误】进入handleAPIError处理:', {
      name: error.name,
      message: error.message,
      code: error.code
    });
    
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error || 'API服务异常';
      
      logger.error('【错误】处理HTTP错误响应:', { status, message });
      
      switch (status) {
        case 401:
          return {
            code: 401,
            message: 'API认证失败，请检查API密钥'
          };
        case 429:
          return {
            code: 429,
            message: '请求过于频繁，请稍后再试'
          };
        default:
          return {
            code: status,
            message: message
          };
      }
    }
    
    if (error.code === 'ECONNREFUSED') {
      logger.error('【错误】处理连接被拒绝错误');
      return {
        code: 503,
        message: '无法连接到API服务'
      };
    }

    logger.error('【错误】处理未知错误');
    return {
      code: 503,
      message: '服务暂时不可用，请稍后再试'
    };
  }
  
  /**
   * 两轮分析 - 真正的两轮推理模型分析
   * @param {Object} userInfo 用户信息
   * @returns {Promise<Object>} 分析结果
   */
  async twoStageAnalysis(userInfo) {
    try {
      // 获取 infoHash，这是必需的
      const infoHash = userInfo.infoHash;
      if (!infoHash) {
        return {
          success: false,
          error: '缺少必要的infoHash字段'
        };
      }
      
      // 确保临时文件目录存在
      const tempDir = path.join(process.cwd(), 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      // 严格使用infoHash作为文件名
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);
      
      // 设置第一轮分析的请求参数
      const firstStageInfo = {
        ...userInfo
      };
      
      // 生成第一轮提示词并进行分析
      const basicPrompt = await this.generatePrompt(firstStageInfo);
      const guidancePart = `请基于此人八字命盘，往命理格局的最高层次指引，分析此人的天赋性格，体貌特征，最适合从事哪些行业以及适合的岗位，适合在哪里发展，事业的大小，身体健康状况。`;
      const firstPrompt = `${basicPrompt}\n${guidancePart}`;

      const firstResponse = await this.analyze(firstPrompt, MODEL_TYPES.REASONER);
      
      if (!firstResponse.success) {
        throw new Error(firstResponse.error || '第一轮推理分析失败');
      }
      
      // 准备更新结果 - 先创建一个包含推理分析结果的对象
      let result = {
        success: true,
        infoHash: infoHash,
        firstStage: {
          reasoningContent: firstResponse.reasoningContent || "",
          content: firstResponse.content || ""
        },
        secondStage: {
          pending: true
        }
      };
      
      // 读取并更新文件，只修改推理分析相关的字段
      let existingData = {};
      if (fs.existsSync(resultFile)) {
        try {
          const existingContent = fs.readFileSync(resultFile, 'utf8');
          existingData = JSON.parse(existingContent);
          logger.info(`【临时文件】读取已有临时文件 [infoHash: ${infoHash}]`);
        } catch (error) {
          logger.warn(`【临时文件】解析已有临时文件失败: ${error.message}，将创建新文件`);
        }
      }
      
      // 合并现有数据和新数据，保留其他字段（如basicAnalysis）
      const updatedData = {
        ...existingData,
        ...result,
        lastUpdateTime: Date.now()
      };
      
      // 确保至少包含submissionId
      if (!updatedData.submissionId && userInfo.submissionId) {
        updatedData.submissionId = userInfo.submissionId;
      }
      
      // 保存更新后的结果到临时文件
      fs.writeFileSync(resultFile, JSON.stringify(updatedData, null, 2));
      
      // 立即发起第二轮分析而不等待其完成
      (async () => {
        try {
          // 构建第二轮分析提示词，基于第一轮结果
          const secondPrompt = `${basicPrompt}\n基于以下八字第一轮分析结果，进一步分析未来大运和流年的关键机遇与挑战，重点关注事业发展和财富量级，尽可能客观理性，不要过度解读：\n\n${firstResponse.content}`;
          
          // 调用推理模型进行第二轮分析
          const secondResponse = await this.analyze(secondPrompt, MODEL_TYPES.REASONER, { timeout: 600000 });
          
          if (!secondResponse.success) {
            return;
          }
          
          // 更新结果对象，添加第二轮分析结果
          try {
            // 读取最新的临时文件内容
            const currentData = JSON.parse(fs.readFileSync(resultFile, 'utf8'));
            
            // 只更新secondStage字段
            currentData.secondStage = {
              pending: false,
              reasoningContent: secondResponse.reasoningContent,
              content: secondResponse.content
            };
            currentData.lastUpdateTime = Date.now();
            
            // 保存更新后的结果
            fs.writeFileSync(resultFile, JSON.stringify(currentData, null, 2));
            logger.info(`【临时文件】第二轮分析结果已更新 [infoHash: ${infoHash}]`);
          } catch (error) {
            // 忽略文件更新错误
          }
        } catch (error) {
          // 忽略第二轮分析错误
        }
      })().catch(() => {
        // 忽略异步任务启动错误
      });
      
      // 立即返回结果
      const responseResult = {
        success: true,
        submissionId: updatedData.submissionId,
        infoHash: infoHash,
        firstStage: updatedData.firstStage,
        secondStage: updatedData.secondStage
      };
      
      // 如果存在基础分析，也一并返回
      if (updatedData.basicAnalysis) {
        responseResult.basicAnalysis = updatedData.basicAnalysis;
      }
      
      return responseResult;
    } catch (error) {
      return {
        success: false,
        error: error.message || '两轮推理分析失败'
      };
    }
  }
  
  /**
   * 多轮对话分析 - 可选择使用chat模型或推理模型
   * @param {Array} messages 历史消息数组
   * @param {string} prompt 当前提示词
   * @param {string} modelType 模型类型，默认为推理模型
   * @returns {Promise<Object>} 分析结果
   */
  async multiTurnAnalysis(messages, prompt, modelType = MODEL_TYPES.REASONER) {
    try {
      // 确保消息数组格式正确
      if (!Array.isArray(messages)) {
        messages = [];
      }
      
      return await this.analyze(prompt, modelType, { history: messages });
    } catch (error) {
      return {
        success: false,
        error: error.message || '多轮对话分析失败'
      };
    }
  }

  /**
   * 获取第二轮分析结果 - 专门从临时文件中读取第二轮分析结果
   * @param {Object} userInfo 包含infoHash的对象
   * @returns {Promise<Object>} 第二轮分析结果
   */
  async getSecondStageAnalysis(userInfo) {
    try {
      // 提取infoHash，这是必需的
      const infoHash = userInfo.infoHash;
      if (!infoHash) {
        return { 
          success: false, 
          error: '缺少必要的infoHash字段',
          code: 'MISSING_HASH'
        };
      }
      
      // 从临时文件中获取结果
      const tempDir = path.join(process.cwd(), 'temp');
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);
      
      // 检查结果文件是否存在
      if (!fs.existsSync(resultFile)) {
        return { 
          success: false, 
          error: '未找到分析结果文件',
          code: 'FILE_NOT_FOUND'
        };
      }
      
      // 读取并解析结果文件
      try {
        const fileContent = fs.readFileSync(resultFile, 'utf8');
        const fullResult = JSON.parse(fileContent);
        
        // 检查是否存在第二轮结果
        if (!fullResult || !fullResult.secondStage) {
          return { 
            success: false, 
            error: '文件中不包含第二轮分析结果',
            code: 'NO_SECOND_STAGE'
          };
        }
        
        // 处理已完成的第二轮分析
        if (!fullResult.secondStage.pending) {
          if (fullResult.secondStage.error) {
            return { 
              success: false, 
              error: fullResult.secondStage.error,
              code: 'ANALYSIS_ERROR'
            };
          }
          
          return { 
            success: true, 
            secondStage: fullResult.secondStage,
            infoHash: infoHash
          };
        }
        
        // 处理进行中的第二轮分析
        if (fullResult.secondStage.pending) {
          const lastUpdateTime = fullResult.lastUpdateTime || 0;
          const waitingTime = Math.floor((Date.now() - lastUpdateTime) / 60000);
          
          // 根据等待时间提供更友好的提示
          let waitMessage = '分析正在进行中，请耐心等待';
          let estimatedTime = '约5-10分钟';
          
          if (waitingTime > 0) {
            if (waitingTime < 5) {
              estimatedTime = '约3-5分钟';
            } else if (waitingTime < 10) {
              estimatedTime = '约1-3分钟';
            } else {
              estimatedTime = '不久后';
            }
            waitMessage = `分析已进行了${waitingTime}分钟，预计${estimatedTime}完成，请耐心等待`;
          }
          
          return { 
            success: true,
            pending: true,
            secondStage: { 
              pending: true, 
              waitingMinutes: waitingTime,
              message: waitMessage,
              estimatedCompletion: estimatedTime
            },
            infoHash: infoHash
          };
        }
      } catch (error) {
        return { 
          success: false, 
          error: `读取或解析结果文件失败: ${error.message}`,
          code: 'READ_ERROR'
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error.message || '获取第二轮分析结果失败',
        code: 'UNKNOWN_ERROR'
      };
    }
  }
}

module.exports = new DeepSeekService();