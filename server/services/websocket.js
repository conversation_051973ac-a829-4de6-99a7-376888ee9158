const socketIO = require('socket.io');
const streamDeepseek = require('./stream-deepseek');
const pythonBazi = require('../utils/python-bazi');
const requestLock = require('./request-lock');
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

/**
 * 设置WebSocket服务
 * @param {Object} server HTTP服务器实例
 * @returns {Object} socket.io实例
 */
function setupWebsocket(server) {
  const io = socketIO(server, {
    cors: {
      origin: "*", // 允许跨域访问，生产环境应限制
      methods: ["GET", "POST"]
    },
    // 使用特定路径，避免与原生WebSocket冲突
    path: '/socket.io-path/'
  });
  
  io.on('connection', (socket) => {
    logger.info(`Socket.IO连接建立: ${socket.id}`);
    
    // 处理八字分析请求
    socket.on('analyzeRequest', async (userData) => {
      // 提取请求ID和infoHash
      const submissionId = userData.submissionId || `sub_${Date.now()}`;
      const infoHash = userData.infoHash;
      
      logger.info(`收到WebSocket分析请求 [ID: ${submissionId}]`, { infoHash });
      
      // 验证必要参数
      const requiredFields = ['year', 'month', 'day', 'hour', 'gender'];
      const missingFields = requiredFields.filter(field => !userData[field]);
      
      if (missingFields.length > 0) {
        logger.warn(`请求缺少必要参数: ${missingFields.join(', ')} [ID: ${submissionId}]`);
        // 统一错误响应格式
        socket.emit('response', {
          code: 400,
          message: `缺少必要参数: ${missingFields.join(', ')}`,
          success: false,
          data: null
        });
        return;
      }
      
      // 检查该infoHash是否已经在处理中
      if (infoHash && requestLock.isLocked(infoHash)) {
        logger.info(`infoHash=${infoHash}的请求已在处理中，等待结果 [ID: ${submissionId}]`);
        socket.emit('response', {
          code: 202,
          message: "分析请求正在处理中，请稍候",
          success: true,
          data: {
            infoHash: infoHash,
            status: "processing"
          }
        });
        return;
      }
      
      // 步骤1: 检查临时文件是否存在已有结果
      if (infoHash) {
        const tempDir = path.join(process.cwd(), 'temp');
        const resultFile = path.join(tempDir, `${infoHash}.result.json`);
        
        if (fs.existsSync(resultFile)) {
          try {
            const fileContent = fs.readFileSync(resultFile, 'utf8');
            const result = JSON.parse(fileContent);
            
            // 如果文件内容有效且包含完整结果，直接返回结果
            if (result.success && result.firstStage) {
              logger.info(`发现已有分析结果，直接返回 [infoHash: ${infoHash}]`);
              socket.emit('response', {
                code: 200,
                message: "使用已有分析结果",
                success: true,
                data: {
                  firstStage: result.firstStage,
                  secondStage: result.secondStage || null  // 第二阶段可能不存在
                }
              });
              return;
            }
          } catch (error) {
            logger.warn(`读取临时文件失败: ${error.message}`, { infoHash });
            // 继续处理，将启动新分析
          }
        }
      }
      
      // 尝试锁定请求
      if (infoHash && !requestLock.tryLock(infoHash)) {
        logger.info(`无法锁定infoHash=${infoHash}的请求，可能已被其他请求锁定 [ID: ${submissionId}]`);
        socket.emit('response', {
          code: 202,
          message: "分析请求正在处理中，请稍候",
          success: true,
          data: {
            infoHash: infoHash,
            status: "processing"
          }
        });
        return;
      }
      
      // 步骤2: 如果没有现有结果，启动流式分析
      socket.emit('analysisStatus', { 
        code: 102, // 处理中
        message: "开始分析...",
        success: true,
        data: {
          stage: "first",
          status: "started"
        }
      });
      
      try {
        // 首先计算八字
        logger.info(`计算八字信息 [ID: ${submissionId}]`);
        let baziInfo;
        try {
          // 确保经度信息正确传递
          if (userData.longitude) {
            userData.longitude = parseFloat(userData.longitude);
          }
          
          // 计算八字
          baziInfo = await pythonBazi.calculateBaziWithPython(userData);
          logger.info(`八字计算完成 [ID: ${submissionId}]`);
        } catch (error) {
          logger.error(`八字计算失败 [ID: ${submissionId}]`, { error: error.message });
          
          // 释放锁
          if (infoHash) requestLock.releaseLock(infoHash);
          
          socket.emit('response', {
            code: 500,
            message: `八字计算失败: ${error.message}`,
            success: false,
            data: null
          });
          return;
        }
        
        // 组合用户数据和八字信息
        const analysisData = { ...userData, ...baziInfo };
        
        // 流式两阶段分析
        const analysisResult = await streamDeepseek.streamTwoStageAnalysis(
          analysisData,
          {}, 
          // 第一阶段回调 - 流式推送内容
          (chunk) => {
            socket.emit('analysisChunk', {
              code: 206, // Partial Content
              message: "部分内容",
              success: true,
              data: {
                stage: 'first',
                type: chunk.type,
                content: chunk.content
              }
            });
          },
          // 第二阶段回调 - 流式推送内容
          (chunk) => {
            socket.emit('analysisChunk', {
              code: 206, // Partial Content
              message: "部分内容",
              success: true,
              data: {
                stage: 'second',
                type: chunk.type,
                content: chunk.content
              }
            });
          },
          true // 自动开始第二阶段
        );
        
        // 第一阶段完成通知
        if (analysisResult.success) {
          socket.emit('analysisStatus', {
            code: 200,
            message: '基础分析完成',
            success: true,
            data: {
              stage: 'first',
              status: 'completed'
            }
          });
          
          // 第二阶段开始通知（仅用于界面提示，实际已在后台自动开始）
          socket.emit('analysisStatus', { 
            code: 102, // 处理中
            message: "开始深度分析...",
            success: true,
            data: {
              stage: "second",
              status: "started"
            }
          });
        } else {
          // 如果分析失败，发送错误
          logger.error(`分析失败 [ID: ${submissionId}]`, { error: analysisResult.error });
          socket.emit('response', {
            code: 500,
            message: analysisResult.error || "分析过程失败",
            success: false,
            data: null
          });
          
          // 释放锁
          if (infoHash) requestLock.releaseLock(infoHash);
        }
        
      } catch (error) {
        logger.error(`分析过程出错 [ID: ${submissionId}]`, { error: error.message });
        
        // 释放锁
        if (infoHash) requestLock.releaseLock(infoHash);
        
        socket.emit('response', {
          code: 500,
          message: error.message || "分析过程出错",
          success: false,
          data: null
        });
      }
    });
    
    // 处理第二阶段分析请求 (用于付费版本)
    socket.on('requestSecondStage', async (data) => {
      const infoHash = data.infoHash;
      
      if (!infoHash) {
        socket.emit('response', {
          code: 400,
          message: "缺少必要的infoHash参数",
          success: false,
          data: null
        });
        return;
      }
      
      logger.info(`收到第二阶段分析请求 [infoHash: ${infoHash}]`);
      
      // TODO: 在这里添加付费验证逻辑
      const isPaid = true; // 默认为true，后续改为实际付费验证
      
      if (!isPaid) {
        socket.emit('response', {
          code: 402, // Payment Required
          message: "需要支付才能进行深度分析",
          success: false,
          data: {
            infoHash: infoHash,
            // 添加支付相关信息
            requiresPayment: true
          }
        });
        return;
      }
      
      // 检查是否存在第一阶段结果
      const tempDir = path.join(process.cwd(), 'temp');
      const resultFile = path.join(tempDir, `${infoHash}.result.json`);
      
      if (!fs.existsSync(resultFile)) {
        socket.emit('response', {
          code: 404,
          message: "未找到相关分析数据，请先完成基础分析",
          success: false,
          data: null
        });
        return;
      }
      
      try {
        const fileContent = fs.readFileSync(resultFile, 'utf8');
        const result = JSON.parse(fileContent);
        
        // 检查是否已有第二阶段结果
        if (result.secondStage) {
          logger.info(`发现已有第二阶段分析结果，直接返回 [infoHash: ${infoHash}]`);
          socket.emit('response', {
            code: 200,
            message: "使用已有分析结果",
            success: true,
            data: {
              secondStage: result.secondStage
            }
          });
          return;
        }
        
        // 如果没有第二阶段结果，但有第一阶段结果，开始第二阶段分析
        if (result.firstStage && result.firstStage.content) {
          // 发送开始通知
          socket.emit('analysisStatus', { 
            code: 102, // 处理中
            message: "开始深度分析...",
            success: true,
            data: {
              stage: "second",
              status: "started"
            }
          });
          
          // TODO: 从文件中获取完整分析数据，并启动第二阶段分析
          // 这里需要补充具体逻辑
        } else {
          socket.emit('response', {
            code: 400,
            message: "第一阶段分析数据不完整，无法进行深度分析",
            success: false,
            data: null
          });
        }
        
      } catch (error) {
        logger.error(`读取或处理分析数据失败 [infoHash: ${infoHash}]`, { error: error.message });
        socket.emit('response', {
          code: 500,
          message: "处理分析数据失败: " + error.message,
          success: false,
          data: null
        });
      }
    });
    
    socket.on('disconnect', () => {
      logger.info(`WebSocket连接断开: ${socket.id}`);
    });
  });
  
  return io;
}

module.exports = setupWebsocket; 