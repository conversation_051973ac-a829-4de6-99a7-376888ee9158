{"name": "bazi-analysis-server", "version": "1.0.0", "description": "Backend server for BaZi analysis application", "main": "app.js", "scripts": {"start": "node app.js", "dev": "NODE_ENV=development nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "ioredis": "^5.3.2", "joi": "^17.13.3", "lunar-javascript": "^1.7.1", "openai": "^4.20.1", "redis": "^4.6.11", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.1"}, "devDependencies": {"nodemon": "^3.0.1"}}