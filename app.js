// app.js

// 方案A：显式分流，最简单也最稳
// DevTools 明确走本地；真机永远走云托管。
// 不改变 socket-client 逻辑；发布体验版/正式版时也不受影响。
wx.setStorageSync('__env_override',
  wx.getSystemInfoSync().platform === 'devtools' ? 'development' : 'cloud');

// Optimization 2: Updated comment to clarify Storage override timing.
// IMPORTANT: To override CURRENT_ENV from config.js using Storage,
// you must call wx.setStorageSync('__env_override', 'your_env') *before* this 'require("./config.js")' statement.
// Alternatively, you can directly override the CURRENT_ENV variable within config.js itself.
const { ENV_ID, LOG_LEVEL, CURRENT_ENV } = require('./config.js'); // 导入 ENV_ID, LOG_LEVEL 和 CURRENT_ENV
const logger = require('./utils/logger');
const socketClient = require('./utils/socket-client'); // 预先加载模块

App({
  onLaunch: function() {
    // --- 可选：环境覆盖 ---
    // The logic to override CURRENT_ENV via Storage is handled within config.js.
    // The wx.setStorageSync call at the top of this file now handles the explicit environment split.
    // --- 环境覆盖结束 ---

    // 设置日志级别 - 使用从 config.js 解构出来的 LOG_LEVEL
    // 确保 LOG_LEVEL 转换为小写，因为 logger 通常期望小写级别
    logger.setLevel((LOG_LEVEL || 'debug').toLowerCase());
    logger.info(`[App] Current Environment (from config initial load): ${CURRENT_ENV}`); // Reflects config's view at time of require

    // ★ wx.cloud.init 调用 ★
    if (wx.cloud) {
      try {
        if (!ENV_ID) {
          logger.error('[App] ENV_ID is not defined in config.js. wx.cloud.init cannot proceed.');
        } else {
          wx.cloud.init({
            env: ENV_ID,
            traceUser: true,
          });
          logger.info('[App] wx.cloud.init called successfully with env:', ENV_ID);
        }
      } catch (e) {
        logger.error('[App] wx.cloud.init failed:', e);
      }
    } else {
      logger.warn('[App] wx.cloud API is not available. wx.cloud.init will not be called.');
    }

    // ★ wx.cloud.init 调用结束 ★

    wx.onError(function(error) {
      logger.error('全局未捕获异常:', error);
    });

    this.initStorage();
    this.initSocketClient();
  },

  initSocketClient: function() {
    try {
      // Pass logger instance directly to getInstance
      const instance = socketClient.getInstance ? socketClient.getInstance(this.globalData.logger) : (socketClient.default ? socketClient.default.getInstance(this.globalData.logger) : null);

      if (!instance) {
        logger.error('[App] Failed to get socketClient instance. It might be null, undefined, or getInstance method is missing.');
        return;
      }

      const systemInfo = wx.getSystemInfoSync();
      instance.envInfo = {
        platform: systemInfo.platform || 'unknown',
        system: systemInfo.system || 'unknown',
        version: this.globalData.version,
        libVersion: systemInfo.SDKVersion || 'unknown'
      };

      // No longer need to manually set instance.logger here as it's passed in getInstance

      this.globalData.socketClient = instance;

      instance.connect().catch(err => {
        logger.error('[App] Initial WebSocket connection failed in initSocketClient:', err);
      });

      logger.info('[App] SocketClient initialized and connection attempt started.');
    } catch (error) {
      logger.error('[App] Failed to initialize SocketClient:', error);
    }
  },

  onShow: function() {
    if (this.globalData.socketClient && typeof this.globalData.socketClient.connect === 'function') {
      this.globalData.socketClient.connect().catch(() => {
        // logger.debug('[App] Attempted to reconnect WebSocket onShow.');
      });
    } else {
      logger.warn('[App] socketClient not found or invalid in globalData onShow, attempting to re-initialize.');
      this.initSocketClient();
    }
  },

  onHide: function() {
    // if (this.globalData.socketClient && typeof this.globalData.socketClient.disconnect === 'function') {
    //   this.globalData.socketClient.disconnect(false);
    //   logger.info('[App] WebSocket disconnected onHide.');
    // }
  },

  initStorage: function() {
    // logger.debug('[App] Local storage initialized.');
  },

  globalData: {
    userInfo: null,
    logger: logger,
    version: '1.3.1', // 小程序版本号示例 (Consider automating this via CI, e.g., git tag/commit)
    socketClient: null,
    // CURRENT_ENV: CURRENT_ENV // If needed globally, can be set here after config load
  }
});