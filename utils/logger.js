/**
 * 微信小程序日志控制模块
 * 提供不同日志级别的控制，并可在发布环境禁用非关键日志
 */

// 日志级别定义
const LogLevel = {
  DEBUG: 0,  // 最详细的调试信息
  INFO: 1,   // 一般信息
  WARN: 2,   // 警告
  ERROR: 3,  // 错误
  NONE: 4    // 不输出任何日志
};

// 环境配置 - 可以根据实际情况修改
const ENV = {
  // 开发环境 - 输出所有级别的日志
  DEVELOPMENT: {
    level: LogLevel.DEBUG,
    outputToConsole: true
  },

  // 测试环境 - 只输出警告和错误
  TESTING: {
    level: LogLevel.WARN,
    outputToConsole: true
  },

  // 生产环境 - 只输出错误
  PRODUCTION: {
    level: LogLevel.ERROR,
    outputToConsole: true
  }
};

// 当前环境设置 - 可以在应用启动时根据实际环境修改
let currentEnv = ENV.DEVELOPMENT; // 默认为开发环境配置

/**
 * 设置当前环境
 * @param {string} env - 环境名称 ('DEVELOPMENT'|'TESTING'|'PRODUCTION')
 */
function setEnvironment(env) {
  if (ENV[env]) {
    currentEnv = ENV[env];
    console.log(`日志级别已设置为: ${getLogLevelName(currentEnv.level)}`);
  } else {
    console.error(`未知环境: ${env}`);
  }
}

/**
 * 获取日志级别名称
 * @param {number} level - 日志级别值
 * @returns {string} 日志级别名称
 */
function getLogLevelName(level) {
  return Object.keys(LogLevel).find(key => LogLevel[key] === level) || 'UNKNOWN';
}

/**
 * 检查是否应该输出此级别的日志
 * @param {number} level - 日志级别
 * @returns {boolean} 是否应输出
 */
function shouldLog(level) {
  return level >= currentEnv.level && currentEnv.outputToConsole;
}

/**
 * 安全处理日志对象，移除敏感信息
 * @param {any} data - 需要处理的日志数据
 * @returns {any} 处理后的安全数据
 */
function sanitizeData(data) {
  if (!data) return data;

  // 如果是对象或数组，则进行深拷贝并处理
  if (typeof data === 'object') {
    try {
      // 深拷贝对象
      const copyData = JSON.parse(JSON.stringify(data));

      // 敏感字段列表 - 可以根据需要扩展
      const sensitiveFields = [
        'password', 'pwd', 'token', 'apiKey', 'api_key',
        'secret', 'DEEPSEEK_API_KEY', 'TENCENT_MAP_KEY'
      ];

      // 递归处理对象中的敏感字段
      function processSensitiveData(obj) {
        if (!obj) return;

        Object.keys(obj).forEach(key => {
          // 检查是否为敏感字段
          if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            obj[key] = '***[REDACTED]***';
          }
          // 递归处理嵌套对象
          else if (typeof obj[key] === 'object' && obj[key] !== null) {
            processSensitiveData(obj[key]);
          }
        });
      }

      processSensitiveData(copyData);
      return copyData;
    } catch (e) {
      // 如果处理失败，返回简单描述避免泄露
      return '[无法安全处理的复杂对象]';
    }
  }

  return data;
}

/**
 * 创建格式化的日志前缀
 * @param {string} level - 日志级别名称
 * @returns {string} 格式化的前缀
 */
function createPrefix(level) {
  const date = new Date();
  const timeStr = date.toLocaleTimeString();
  return `[${timeStr}][${level}]`;
}

// 导出日志函数
module.exports = {
  LogLevel,

  // 设置环境
  setEnvironment,

  /**
   * 设置日志级别
   * @param {string|number} level - 日志级别名称或值
   */
  setLevel: function(level) {
    if (typeof level === 'string') {
      // 如果是字符串，尝试将其转换为对应的日志级别
      const levelUpper = level.toUpperCase();
      if (LogLevel[levelUpper] !== undefined) {
        currentEnv.level = LogLevel[levelUpper];
        console.log(`日志级别已设置为: ${levelUpper}`);
      } else {
        console.error(`未知日志级别: ${level}`);
      }
    } else if (typeof level === 'number') {
      // 如果是数字，直接设置
      currentEnv.level = level;
      console.log(`日志级别已设置为: ${getLogLevelName(level)}`);
    } else {
      console.error(`无效的日志级别: ${level}`);
    }
  },

  // 调试信息 - 只用于开发环境
  debug: function(message, ...data) {
    if (shouldLog(LogLevel.DEBUG)) {
    }
  },

  // 一般信息
  info: function(message, ...data) {
    if (shouldLog(LogLevel.INFO)) {
      console.log(createPrefix('INFO'), message, ...data.map(sanitizeData));
    }
  },

  // 警告信息
  warn: function(message, ...data) {
    if (shouldLog(LogLevel.WARN)) {
      console.warn(createPrefix('WARN'), message, ...data.map(sanitizeData));
    }
  },

  // 错误信息
  error: function(message, ...data) {
    if (shouldLog(LogLevel.ERROR)) {
      console.error(createPrefix('ERROR'), message, ...data.map(sanitizeData));
    }
  }
};