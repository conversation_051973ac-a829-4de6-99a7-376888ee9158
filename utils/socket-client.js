// miniprogram/utils/socket-client.js
console.log('socket-client build-id', Date.now());

// 从 ../config.js 引入整个配置对象，以便动态访问
const configModule = require('../config');

// 注意：移除了在此文件中的 CLOUDRUN_* 常量定义
// 注意：移除了在此文件中的 wx.cloud.init() 调用，应由 app.js 统一处理
// 注意：移除了在此文件中的 CURRENT_ENV_FLAG 硬编码

class SocketClient {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.connecting = false;
    this.handlers = {};
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.maxReconnectInterval = 30000;
    this.logger = console;
    this.envInfo = null;
    this.heartbeatTimer = null;
    this.heartbeatInterval = 20000;
    this.consumerCount = 0;
    this.hasAttemptedSwitchToCloud = false; // 新增标志，防止在一次connect()生命周期内重复切换
  }

  // 更新点 1: _commonSocketSetup 的定义里接收 failConnectionAttempt
  _commonSocketSetup(socketInstance, resolveConnectPromise, isCloudConnect, connectTimerRef, failConnectionAttempt) {
    this.socket = socketInstance;

    this.socket.onOpen(() => {
      if (connectTimerRef && connectTimerRef.timer) {
        clearTimeout(connectTimerRef.timer);
        connectTimerRef.timer = null; // 清除后置空
      }
      
      this.connected = true;
      this.connecting = false;
      this.reconnectAttempts = 0; // 成功连接后重置重连尝试
      this.hasAttemptedSwitchToCloud = false; // 成功连接后重置切换尝试标志
      this._setupHeartbeat();
      this.logger.debug('[Socket] Sending initial heartbeat on connection open.');
      try { this._sendHeartbeat(); } catch (e) { this.logger.warn('[Socket] Initial heartbeat send call failed unexpectedly:', e); }
      this._trigger('connect');
      resolveConnectPromise(true);
    });

    this.socket.onClose((e) => {
      this.logger.warn('[Socket] WebSocket connection closed.', { code: e?.code, reason: e?.reason });
      const wasConnected = this.connected;
      this.connected = false;
      this._clearHeartbeat();
      this._trigger('disconnect', e);

      // 更新点 2: onClose 内 “握手未成功就关掉” 分支
      if (!wasConnected && this.connecting) {
        this.logger.warn('[Socket] Connection closed before it was fully opened (onClose during handshake).');
        this.connecting = false; // 必须在调用 failConnectionAttempt 之前设置
                                  // 否则 failConnectionAttempt 内部的 this.connecting 还是 true
                                  // 且 failConnectionAttempt 内部会再次设置 this.connecting = false
        if (connectTimerRef && connectTimerRef.timer) { // 确保计时器被清除
            clearTimeout(connectTimerRef.timer);
            connectTimerRef.timer = null;
        }
        failConnectionAttempt('Handshake closed before OPEN', e); // 调用传入的回调
        return;
      }

      // 如果之前是连接状态，并且不是正常关闭(1000)，且重连次数未达上限
      if (wasConnected && e?.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this._scheduleReconnect();
      } else if (e?.code === 1000) {
        this.logger.info('[Socket] Connection closed normally (code 1000).');
        this.connecting = false; // 确保状态正确
      } else {
        // 其他非握手阶段的关闭，且 this.connecting 为 false
        this.connecting = false;
      }
    });

    this.socket.onError((err) => {
      this.logger.error('[Socket] WebSocket error occurred:', err);

      // 更新点 2: onError 里的同类分支也一样回调
      if (this.connecting && !this.connected) { // 错误发生在连接过程中，且连接未成功建立
        this.logger.warn('[Socket] WebSocket error during connection attempt (onError before OPEN).');
        this.connecting = false; // 必须在调用 failConnectionAttempt 之前设置
        if (connectTimerRef && connectTimerRef.timer) { // 确保计时器被清除
            clearTimeout(connectTimerRef.timer);
            connectTimerRef.timer = null;
        }
        this._trigger('error', err); // 触发错误事件
        failConnectionAttempt('onError before OPEN', err); // 调用传入的回调
        return;
      }

      // 如果连接已建立后发生错误，通常 onClose 也会被触发
      // 此处 onError 主要处理那些 onClose 可能未覆盖的、或更早期的错误
      // 如果 this.connecting 为 true 但 this.connected 也为 true (虽然理论上 onOpen 后 connecting=false)
      // 或者 this.connecting 为 false 但 this.connected 为 true (已连接后发生的错误)
      // 这些情况通常会伴随 onClose，让 onClose 处理重连等逻辑
      if(this.connected) { // 如果是已连接状态下的错误
        // this.connecting 应该已经是 false 了
      } else if (this.connecting) { // 如果仍在连接中 (理论上被上面的 if 分支覆盖)
        this.connecting = false;
      }

      this._clearHeartbeat(); // 清理心跳是安全的
      this._trigger('error', err); // 对于其他错误，也触发error事件
    });

    this.socket.onMessage((msg) => this._handleMessage(msg));
  }

  connect() {
    if (!this.envInfo) {
      try {
        const systemInfo = wx.getSystemInfoSync();
        this.envInfo = {
          platform: systemInfo.platform || 'unknown',
          system: systemInfo.system || 'unknown',
          version: 'N/A',
          SDKVersion: systemInfo.SDKVersion || 'unknown',
          libVersion: 'client-1.0.0'
        };
        if (typeof getApp === 'function') {
            const appInstance = getApp();
            if (appInstance && appInstance.globalData && appInstance.globalData.version) {
                this.envInfo.version = appInstance.globalData.version;
            }
        }
      } catch (e) {
        this.envInfo = { platform: 'unknown', system: 'unknown', version: 'N/A', SDKVersion: 'unknown', libVersion: 'client-1.0.0' };
        this.logger.warn('[Socket] 获取系统信息失败，使用默认 envInfo', e);
      }
    }

    if (this.connected) {
      this.logger.debug('[Socket] connect() called, already connected.');
      return Promise.resolve(true);
    }

    if (this.connecting) {
      this.logger.debug('[Socket] connect() called, connection in progress, awaiting result...');
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (this.connected) {
            clearInterval(checkInterval);
            resolve(true);
          } else if (!this.connecting) {
            clearInterval(checkInterval);
            resolve(this.connected);
          }
        }, 100);
      });
    }

    this.connecting = true;

    let currentEffectiveEnv = configModule.CURRENT_ENV;
    const envOverride = wx.getStorageSync('__env_override');
    if (envOverride && configModule.WS_ENV[envOverride]) {
      currentEffectiveEnv = envOverride;
    }
    const wsEnvConfig = configModule.WS_ENV;
    const cloudEnvId = configModule.ENV_ID;
    const cloudServiceName = configModule.SERVICE_NAME;
    const initialConfiguredEnv = configModule.CURRENT_ENV;


    this.logger.info(`[Socket] Attempting to connect (Effective Env for this attempt: ${currentEffectiveEnv}). Reconnect attempt: ${this.reconnectAttempts}`);

    return new Promise((resolveConnect) => {
      const connectTimerRef = { timer: null }; // 使用对象包装以便传递引用
      const connectTimeoutDuration = 10000;

      const failConnectionAttempt = (reason, errorDetails = null) => {
        // 辅助小修: 确认二次 clearTimeout 不报错
        if (connectTimerRef && connectTimerRef.timer) {
          clearTimeout(connectTimerRef.timer);
          connectTimerRef.timer = null; // 清除后置空，防止被意外再次清除
        }

        // this.connecting 应当在调用此函数前，由 onClose/onError 或 setTimeout 设置为 false
        // 但为保险起见，再次确认
        if (this.connecting) {
            this.logger.warn(`[Socket] failConnectionAttempt called while this.connecting was still true. Reason: ${reason}. Setting to false.`);
            this.connecting = false;
        }


        if (currentEffectiveEnv === 'development' && this.reconnectAttempts === 0 && !this.hasAttemptedSwitchToCloud) {
          this.logger.warn(`[Socket] Local (development) connect failed: ${reason}. Switching to cloud and retrying.`);
          wx.setStorageSync('__env_override', 'cloud');
          this.hasAttemptedSwitchToCloud = true;
          // this.connecting is already false or will be set by caller.
          // A new connect() attempt will be made.
          this.connect().then(resolveConnect).catch(e => { // 用新的 connect 结果来 resolve/reject 外层 Promise
            this.logger.error('[Socket] Switched-to-cloud connection attempt also failed:', e);
            // 如果切换到云也失败，则原始连接算失败
            // 清理可能存在的旧socket (此时 socket 可能已在 onError/onClose 中被处理)
            if (this.socket && typeof this.socket.close === 'function') {
                try { this.socket.close({code: 1001, reason: "Switched cloud connect failed"}); } catch(closeErr){}
            }
            this.socket = null;
            this._scheduleReconnect(); // 云连接失败，按原计划安排重连 (此时 reconnectAttempts 应该 > 0)
            resolveConnect(false); // 明确表示原始的 connect() 调用失败
          });
          return;
        }

        this.logger.warn(`[Socket] Connection attempt failed: ${reason}`, errorDetails || '');
        if (this.socket && typeof this.socket.close === 'function' && !this.connected) {
          try {
            // 1001 Going Away, 1006 Abnormal Closure.
            // If failConnectionAttempt is called due to onClose(1006) or onError, socket might already be closed or in a bad state.
            // Avoid calling close if it's already in the process of closing or closed.
            // However, wx.SocketTask.close() is idempotent on state, so it's generally safe.
            this.socket.close({ code: 1001, reason: `Connection Attempt Failed: ${reason}` });
          } catch (closeErr) {
            this.logger.warn('[Socket] Error trying to close socket on failed connection attempt:', closeErr);
          }
        }
        this.socket = null; // 确保 socket 被清理
        this._scheduleReconnect();
        resolveConnect(false);
      };

      try {
        if (typeof wx === 'undefined') {
           this.logger.error('[Socket] CRITICAL: wx API object is undefined. Cannot proceed.');
           this.connecting = false;
           return resolveConnect(false);
        }

        const shouldUseCloudConnect = (currentEffectiveEnv === 'cloud');

        connectTimerRef.timer = setTimeout(() => {
          // 只有当连接仍在进行中 (this.connecting) 且尚未成功 (this.connected) 时，超时才应触发失败
          if (this.connecting && !this.connected) {
            this.logger.warn('[Socket] Connection attempt timed out.');
            this.connecting = false; // 在调用 failConnectionAttempt 之前设置
            failConnectionAttempt('Connection Timeout');
          } else if (connectTimerRef.timer) {
            // 如果不是 connecting 状态了，说明可能已连接或已被其他逻辑处理，清除计时器即可
            clearTimeout(connectTimerRef.timer);
            connectTimerRef.timer = null;
          }
        }, connectTimeoutDuration);

        if (shouldUseCloudConnect) {

          wx.cloud.connectContainer({
            config: { env: cloudEnvId },
            service: cloudServiceName,
            path: '/',
            timeout: connectTimeoutDuration
          }).then(({ socketTask }) => {
            if (!socketTask || typeof socketTask.onOpen !== 'function') {
                this.logger.error('[Socket] wx.cloud.connectContainer resolved but socketTask is invalid or missing.');
                this.connecting = false;
                failConnectionAttempt('Invalid socketTask from wx.cloud.connectContainer');
                return;
            }
            this.logger.debug('[Socket] wx.cloud.connectContainer successfully resolved with socketTask.');
            // 更新点 1: connect() 里调用位置, 新增 failConnectionAttempt
            this._commonSocketSetup(socketTask, resolveConnect, shouldUseCloudConnect, connectTimerRef, failConnectionAttempt);
          }).catch(err => {
            this.logger.error('[Socket] wx.cloud.connectContainer promise rejected or call failed:', err);
            this.connecting = false;
            failConnectionAttempt('wx.cloud.connectContainer call failed', err);
          });

        } else {

          let wsUrl = wsEnvConfig[currentEffectiveEnv];
          this.logger.info('[Socket] Connecting to WebSocket URL:', wsUrl);

          try {
            const socketTask = wx.connectSocket({
              url: wsUrl,
              protocols: ['json'],
              header: {
                'content-type': 'application/json',
                'x-client-version': this.envInfo.version || 'unknown',
                'x-client-id': `wx_${Date.now()}_${Math.random().toString(36).substring(2,10)}`,
                'x-client-env': `${this.envInfo.platform || 'unknown'},${this.envInfo.system || 'unknown'},SDK:${this.envInfo.SDKVersion || 'unknown'}`
              },
              tcpNoDelay: true,
            });
            this.logger.debug('[Socket] wx.connectSocket API call initiated, setting up listeners.');
            // 更新点 1: connect() 里调用位置, 新增 failConnectionAttempt
            this._commonSocketSetup(socketTask, resolveConnect, shouldUseCloudConnect, connectTimerRef, failConnectionAttempt);
          } catch (apiCallError) {
            this.logger.error('[Socket] wx.connectSocket API call itself failed:', apiCallError);
            this.connecting = false;
            failConnectionAttempt('wx.connectSocket API call exception', apiCallError);
          }
        }
      } catch (error) {
        this.logger.error('[Socket] Exception during WebSocket instance creation or initial setup phase:', error);
        this.connecting = false;
        if (connectTimerRef && connectTimerRef.timer) { // 确保清除
            clearTimeout(connectTimerRef.timer);
            connectTimerRef.timer = null;
        }
        // 对于这种顶层的、早期的错误，也应该通过 failConnectionAttempt 来处理
        // 以确保重连逻辑和云切换逻辑能够被触发
        failConnectionAttempt('Connect method top-level exception', error);
        // resolveConnect(false); // failConnectionAttempt 会处理 resolveConnect
      }
    });
  }

  isConnected() {
    return this.connected;
  }

  disconnect(reasonCode = 1000, reasonMessage = 'Client initiated disconnect') {
    this.logger.info(`[Socket] disconnect() called. Reason: ${reasonMessage} (Code: ${reasonCode})`);
    this._clearHeartbeat();
    this.reconnectAttempts = this.maxReconnectAttempts; 
    if (this.socket && typeof this.socket.close === 'function') {
      try {
        this.socket.close({
          code: reasonCode,
          reason: reasonMessage
        });
      } catch (e) {
        this.logger.warn('[Socket] Exception while trying to close socket during disconnect():', e, '(Socket might have been already closed)');
      }
    }
    this.socket = null;
    this.connected = false;
    this.connecting = false;
  }

  acquire() {
    this.consumerCount += 1;
    this.logger.debug(`[Socket] acquire() called. Consumer count: ${this.consumerCount}`);
  }

  release() {
    if (this.consumerCount <= 0) {
      this.consumerCount = 0;
      return;
    }

    if (this.consumerCount === 1) {
      this.logger.debug('[Socket] Last consumer releasing. Scheduling delayed decrement (3s) to potentially manage connection lifecycle (currently heartbeat-driven).');
      setTimeout(() => {
        if (this.consumerCount === 1) {
          this.consumerCount = 0;
          this.logger.debug(`[Socket] Delayed release executed. Consumer count is now 0.`);
        } else {
          this.logger.debug(`[Socket] Delayed release: consumer count changed during delay. Current: ${this.consumerCount}. No action taken.`);
        }
      }, 3000);
    } else {
      this.consumerCount -= 1;
      this.logger.debug(`[Socket] release() called. Consumer count: ${this.consumerCount}`);
    }
  }

  async _ensureConnection() {
    if (!this.isConnected()) {
      this.logger.info('[Socket] _ensureConnection: Not connected. Attempting to connect...');
      const connected = await this.connect();
      if (!connected) {
          this.logger.error('[Socket] _ensureConnection: Failed to establish connection.');
          throw new Error('Failed to establish WebSocket connection (ensureConnection).');
      }
      this.logger.debug('[Socket] _ensureConnection: Connection established.');
    }
  }

  _send(data, tag = '消息') {
    if (data && typeof data === 'object') {
      const fixZero = (v) => (v === 0 ? '0' : v);
      if (data.data && typeof data.data === 'object') {
        if (Object.prototype.hasOwnProperty.call(data.data, 'hour')) data.data.hour = fixZero(data.data.hour);
        if (Object.prototype.hasOwnProperty.call(data.data, 'minute')) data.data.minute = fixZero(data.data.minute);
      } else {
        if (Object.prototype.hasOwnProperty.call(data, 'hour')) data.hour = fixZero(data.hour);
        if (Object.prototype.hasOwnProperty.call(data, 'minute')) data.minute = fixZero(data.minute);
      }
    }

    return new Promise((resolve, reject) => {
      if (!this.isConnected() || !this.socket || typeof this.socket.send !== 'function') {
         this.logger.error(`[Socket] ${tag} send failed - WebSocket not connected or socket object/send method invalid.`);
         return reject(new Error('WebSocket not connected or socket/send method invalid.'));
      }
      try {
        const jsonData = JSON.stringify(data);
        this.socket.send({
          data: jsonData,
          success: () => {
            this.logger.debug(`[Socket] ${tag} sent successfully:`, data);
            resolve(true);
          },
          fail: (err) => {
            this.logger.error(`[Socket] ${tag} send failed (API fail callback):`, err, 'Data:', data);
            reject(err);
          }
        });
      } catch (e) {
          this.logger.error(`[Socket] Exception during ${tag} send attempt:`, e, 'Data:', data);
          reject(e);
      }
    });
  }

  async sendAnalyzeRequest(userData) {
    await this._ensureConnection();
    return this._send({ type: 'startAnalysis', data: userData }, '分析请求');
  }

  async requestSecondStage(data) {
    await this._ensureConnection();
    return this._send({ type: 'requestSecondStage', data }, '第二阶段请求');
  }

  async send(data) {
    if (typeof data !== 'object' || data === null) {
       this.logger.warn('[Socket] send() called with non-object data:', data);
       return Promise.reject(new Error('Data to send must be an object.'));
    }
    if (!data.type) {
        this.logger.warn('[Socket] send() called, data is missing "type" field:', data);
        return Promise.reject(new Error('Data to send must have a "type" field.'));
    }
    await this._ensureConnection();
    return this._send(data, data.type || '普通消息');
  }

  _handleMessage(message) {
    try {
      let payload = message.data;

      if (typeof payload === 'string') {
        try {
          const parsed = JSON.parse(payload);
          if (parsed && typeof parsed === 'object') payload = parsed;
        } catch (e) {
          this.logger.warn('[Socket] Received non-JSON string message, or JSON parse error:', payload, e);
          this._trigger('message', payload);
          return;
        }
      }

      if (typeof payload !== 'object' || payload === null) {
        this.logger.warn('[Socket] Message payload is not an object (or JSON parsed to non-object):', payload);
        this._trigger('message', payload);
        return;
      }

      if ('body' in payload) {
        this.logger.warn('[Socket] payload.body detected, deleting it to prevent downstream misuse (e.g. getReader error). Original body was:', payload.body);
        delete payload.body;
        this.logger.debug('[Socket] payload.body deleted. Current payload:', payload);
      }
      
      if (payload.type) {
        this.logger.debug(`[Socket] Dispatching typed event <${payload.type}> with payload:`, payload);
        this._trigger(payload.type, payload);
        return;
      }

      if (Object.prototype.hasOwnProperty.call(payload, 'code')) {
        this.logger.debug(`[Socket] Message has 'code': ${payload.code}, but no 'type'.`);
        if (payload.code === 206) this._trigger('analysisChunk', payload);
        else if (payload.code >= 100 && payload.code < 200) this._trigger('analysisStatus', payload);
        else this._trigger('response', payload);
        return;
      }

      this.logger.warn('[Socket] Received message with unknown structure (no "type", no recognized "code"):', payload);
      this._trigger('message', payload);

    } catch (err) {
      this.logger.error('[Socket] Exception in _handleMessage:', err, 'Original message:', message);
      this._trigger('messageError', { error: err, originalMessage: message });
    }
  }

  _sendHeartbeat() {
    if (!this.isConnected() || !this.socket || typeof this.socket.send !== 'function') {
      this.logger.debug('[Socket] _sendHeartbeat: Cannot send, not connected or socket/send invalid.');
      return;
    }
    const heartbeatMsg = { type: 'heartbeat', data: { timestamp: Date.now() } };
    try {
      this.socket.send({ data: JSON.stringify(heartbeatMsg),
        success: () => { /* this.logger.debug('[Socket] Heartbeat sent successfully.'); */ },
        fail: (err) => { this.logger.warn('[Socket] Heartbeat send failed (API fail callback):', err); }
      });
    } catch (e) { this.logger.error('[Socket] Exception during _sendHeartbeat execution:', e); }
  }

  _setupHeartbeat() {
    this._clearHeartbeat();
    if (!this.isConnected()) {
        this.logger.warn('[Socket] _setupHeartbeat: Cannot start, not connected.'); return;
    }
    this.logger.debug(`[Socket] Starting heartbeat timer. Interval: ${this.heartbeatInterval}ms`);
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected() && this.socket) {
        this._sendHeartbeat();
      } else {
        this.logger.warn('[Socket] Heartbeat interval: Connection lost or socket invalid. Stopping heartbeat.');
        this._clearHeartbeat();
      }
    }, this.heartbeatInterval);
  }

  _clearHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer); this.heartbeatTimer = null;
      this.logger.debug('[Socket] Heartbeat timer stopped.');
    }
  }

  _scheduleReconnect() {
    if (this.connecting) {
        this.logger.info('[Socket] _scheduleReconnect: Connection attempt already in progress. Aborting new reconnect schedule.');
        return;
    }
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.warn(`[Socket] Maximum reconnect attempts (${this.maxReconnectAttempts}) reached. Stopping reconnection attempts.`);
      this._trigger('reconnectFailed');
      return;
    }
    const baseDelay = this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts);
    const jitter = Math.random() * 1000;
    const delay = Math.min(baseDelay + jitter, this.maxReconnectInterval);

    this.reconnectAttempts += 1;
    this.logger.info(`[Socket] Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay.toFixed(0)}ms.`);

    setTimeout(() => {
      if (!this.connected && !this.connecting) {
        this.logger.info(`[Socket] Executing reconnect attempt ${this.reconnectAttempts}.`);
        this.connect().catch(err => {
            this.logger.error(`[Socket] Reconnect attempt ${this.reconnectAttempts} (via connect() call from timeout) resulted in a caught error:`, err);
        });
      } else {
        this.logger.info('[Socket] Reconnect timer fired, but connection is now active or already connecting. Aborting this attempt.');
        if (this.connected) this.reconnectAttempts = 0;
      }
    }, delay);
  }

  on(event, callback) {
    if (typeof callback !== 'function') { this.logger.warn(`[Socket] Attempted to register non-function callback for event <${event}>.`); return; }
    if (!this.handlers[event]) this.handlers[event] = [];
    if (!this.handlers[event].includes(callback)) {
        this.handlers[event].push(callback);
        this.logger.debug(`[Socket] Event listener added for <${event}>.`);
    } else this.logger.debug(`[Socket] Event listener for <${event}> already exists.`);
  }

  off(event, callback) {
    if (!this.handlers[event]) { this.logger.debug(`[Socket] off() called for event <${event}>, but no listeners registered.`); return; }
    if (!callback) {
      delete this.handlers[event];
      this.logger.debug(`[Socket] All listeners removed for event <${event}>.`);
    } else {
      const initialLength = this.handlers[event].length;
      this.handlers[event] = this.handlers[event].filter(fn => fn !== callback);
      if (this.handlers[event].length < initialLength) this.logger.debug(`[Socket] Specific listener removed for event <${event}>.`);
      if (this.handlers[event].length === 0) {
          delete this.handlers[event];
          this.logger.debug(`[Socket] Event <${event}> has no more listeners, removing event key.`);
      }
    }
  }

  _trigger(event, data) {
    if (!this.handlers[event] || this.handlers[event].length === 0) {
        return;
    }
    
    this.logger.debug(`[Socket] Triggering event <${event}>. Number of listeners: ${this.handlers[event].length}. Data:`, data);

    const handlersToExecute = [...this.handlers[event]];
    handlersToExecute.forEach(fn => {
      try { fn(data); } catch (err) {
        this.logger.error(`[Socket] Exception in event listener for <${event}>:`, err, 'Data:', data);
      }
    });
  }
} // END OF SocketClient CLASS

// Singleton Export
let _instance = null;
module.exports = {
  getInstance(loggerInstance) {
    if (!_instance) {
      _instance = new SocketClient();
    }
    if (loggerInstance) {
      _instance.logger = loggerInstance;
    } else if (_instance.logger === console && typeof getApp === 'function') {
      const app = getApp();
      if (app && app.globalData && app.globalData.logger) {
        _instance.logger = app.globalData.logger;
      }
    }
    return _instance;
  },
  acquire() { if(_instance) _instance.acquire(); else console.warn('[Socket] acquire called before getInstance.');},
  release() { if(_instance) _instance.release(); else console.warn('[Socket] release called before getInstance.');},

  _resetInstance() { 
    if (_instance) {
      _instance.disconnect(1000, 'Instance reset by _resetInstance');
    }
    _instance = null;
    console.log('[Socket] Singleton instance has been reset.');
  }
};