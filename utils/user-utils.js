/**
 * 用户工具模块
 * 统一管理用户登录状态检查和用户信息处理
 */

/**
 * 检查用户是否已登录
 * @returns {Object|null} 用户信息或null
 */
function checkUserLogin() {
  try {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.userId) {
      return userInfo;
    }
    return null;
  } catch (error) {
    console.error('[UserUtils] 检查用户登录状态失败:', error);
    return null;
  }
}

/**
 * 强制用户登录检查，如果未登录则跳转到登录页
 * @param {string} currentPage 当前页面名称，用于日志记录
 * @returns {Object|null} 用户信息或null（如果跳转到登录页）
 */
function requireUserLogin(currentPage = 'Unknown') {
  const userInfo = checkUserLogin();
  
  if (!userInfo) {
    console.log(`[UserUtils] ${currentPage}页面：用户未登录，跳转到登录页`);
    
    wx.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 1500
    });
    
    setTimeout(() => {
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }, 1500);
    
    return null;
  }
  
  console.log(`[UserUtils] ${currentPage}页面：用户已登录 -`, userInfo.name);
  return userInfo;
}

/**
 * 保存用户登录信息
 * @param {Object} userInfo 用户信息
 */
function saveUserInfo(userInfo) {
  try {
    wx.setStorageSync('userInfo', userInfo);
    console.log('[UserUtils] 用户信息已保存:', userInfo.name);
  } catch (error) {
    console.error('[UserUtils] 保存用户信息失败:', error);
    throw error;
  }
}

/**
 * 清除用户登录信息（退出登录）
 */
function clearUserInfo() {
  try {
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('formInfo'); // 同时清除表单信息
    console.log('[UserUtils] 用户信息已清除');
  } catch (error) {
    console.error('[UserUtils] 清除用户信息失败:', error);
  }
}

/**
 * 获取表单信息
 * @returns {Object|null} 表单信息或null
 */
function getFormInfo() {
  try {
    return wx.getStorageSync('formInfo');
  } catch (error) {
    console.error('[UserUtils] 获取表单信息失败:', error);
    return null;
  }
}

/**
 * 保存表单信息
 * @param {Object} formInfo 表单信息
 */
function saveFormInfo(formInfo) {
  try {
    wx.setStorageSync('formInfo', formInfo);
    console.log('[UserUtils] 表单信息已保存');
  } catch (error) {
    console.error('[UserUtils] 保存表单信息失败:', error);
    throw error;
  }
}

/**
 * 用户登录成功后的处理
 * @param {Object} userInfo 用户信息
 * @param {string} redirectUrl 登录成功后跳转的页面，默认为功能页
 */
function handleLoginSuccess(userInfo, redirectUrl = '/pages/features/features') {
  // 保存用户信息
  saveUserInfo(userInfo);
  
  // 显示成功提示
  wx.showToast({
    title: '登录成功',
    icon: 'success',
    duration: 1500
  });
  
  // 跳转到指定页面
  setTimeout(() => {
    wx.redirectTo({
      url: redirectUrl
    });
  }, 1500);
}

/**
 * 用户注册成功后的处理
 * @param {Object} userInfo 用户信息
 * @param {string} redirectUrl 注册成功后跳转的页面，默认为功能页
 */
function handleRegisterSuccess(userInfo, redirectUrl = '/pages/features/features') {
  // 保存用户信息
  saveUserInfo(userInfo);
  
  // 清除注册临时信息
  try {
    wx.removeStorageSync('registerInfo');
  } catch (error) {
    console.warn('[UserUtils] 清除注册临时信息失败:', error);
  }
  
  // 显示成功提示
  wx.showToast({
    title: '注册成功',
    icon: 'success',
    duration: 1500
  });
  
  // 跳转到指定页面
  setTimeout(() => {
    wx.redirectTo({
      url: redirectUrl
    });
  }, 1500);
}

/**
 * 用户退出登录
 * @param {string} redirectUrl 退出后跳转的页面，默认为登录页
 */
function handleLogout(redirectUrl = '/pages/login/login') {
  // 清除用户信息
  clearUserInfo();
  
  // 显示提示
  wx.showToast({
    title: '已退出登录',
    icon: 'success',
    duration: 1500
  });
  
  // 跳转到登录页
  setTimeout(() => {
    wx.redirectTo({
      url: redirectUrl
    });
  }, 1500);
}

/**
 * 获取用户显示名称
 * @param {Object} userInfo 用户信息
 * @returns {string} 显示名称
 */
function getUserDisplayName(userInfo) {
  if (!userInfo) return '未知用户';
  
  if (userInfo.name) return userInfo.name;
  if (userInfo.email) return userInfo.email;
  if (userInfo.phone) return userInfo.phone;
  
  return '用户' + (userInfo.userId ? userInfo.userId.slice(-6) : '');
}

module.exports = {
  checkUserLogin,
  requireUserLogin,
  saveUserInfo,
  clearUserInfo,
  getFormInfo,
  saveFormInfo,
  handleLoginSuccess,
  handleRegisterSuccess,
  handleLogout,
  getUserDisplayName
};
