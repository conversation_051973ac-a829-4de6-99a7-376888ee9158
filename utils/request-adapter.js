/**
 * 统一请求适配器
 * 根据环境自动选择使用 wx.request 或 wx.cloud.callContainer
 */

const config = require('../config');

/**
 * 统一请求方法
 * @param {Object} options 请求选项
 * @param {string} options.url 请求URL（相对路径，如 '/api/v1/user/register'）
 * @param {string} options.method 请求方法
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {number} options.timeout 超时时间
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 * @param {Function} options.complete 完成回调
 * @returns {Promise} 请求Promise
 */
function request(options) {
  const currentEnv = config.CURRENT_ENV;
  
  // 如果是云环境，使用 wx.cloud.callContainer
  if (currentEnv === 'cloud') {
    return callContainer(options);
  } else {
    // 其他环境使用 wx.request
    return wxRequest(options);
  }
}

/**
 * 使用 wx.request 发起请求
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
function wxRequest(options) {
  return new Promise((resolve, reject) => {
    const currentEnv = config.CURRENT_ENV;
    const apiBaseUrl = config.API_ENV[currentEnv];
    
    // 构建完整URL
    const fullUrl = options.url.startsWith('http') ? options.url : `${apiBaseUrl}${options.url}`;
    
    wx.request({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'content-type': 'application/json',
        ...options.header
      },
      timeout: options.timeout || 30000,
      success: (res) => {
        if (options.success) options.success(res);
        resolve(res);
      },
      fail: (error) => {
        if (options.fail) options.fail(error);
        reject(error);
      },
      complete: (res) => {
        if (options.complete) options.complete(res);
      }
    });
  });
}

/**
 * 使用 wx.cloud.callContainer 发起请求
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
function callContainer(options) {
  return new Promise((resolve, reject) => {
    // 确保云开发已初始化
    if (!wx.cloud) {
      const error = new Error('wx.cloud is not available');
      if (options.fail) options.fail(error);
      reject(error);
      return;
    }
    
    wx.cloud.callContainer({
      config: {
        env: config.ENV_ID
      },
      path: options.url, // 直接使用相对路径
      header: {
        "X-WX-SERVICE": config.SERVICE_NAME,
        "content-type": 'application/json',
        ...options.header
      },
      method: options.method || 'POST',
      data: options.data
    }).then((res) => {
      // 适配响应格式，使其与 wx.request 保持一致
      const adaptedRes = {
        data: res.data,
        statusCode: res.statusCode || 200,
        header: res.header || {},
        errMsg: res.errMsg || 'ok'
      };
      
      if (options.success) options.success(adaptedRes);
      if (options.complete) options.complete(adaptedRes);
      resolve(adaptedRes);
    }).catch((error) => {
      if (options.fail) options.fail(error);
      if (options.complete) options.complete(error);
      reject(error);
    });
  });
}

/**
 * GET 请求快捷方法
 * @param {string} url 请求URL
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求Promise
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST 请求快捷方法
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 其他选项
 * @returns {Promise} 请求Promise
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT 请求快捷方法
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 其他选项
 * @returns {Promise} 请求Promise
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE 请求快捷方法
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} options 其他选项
 * @returns {Promise} 请求Promise
 */
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
}

/**
 * 获取当前环境信息
 * @returns {Object} 环境信息
 */
function getEnvInfo() {
  return {
    currentEnv: config.CURRENT_ENV,
    envId: config.ENV_ID,
    serviceName: config.SERVICE_NAME,
    apiBaseUrl: config.API_ENV[config.CURRENT_ENV],
    wsBaseUrl: config.WS_ENV[config.CURRENT_ENV]
  };
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  getEnvInfo,
  // 导出内部方法供调试使用
  wxRequest,
  callContainer
};
