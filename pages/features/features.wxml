<!-- pages/features/features.wxml -->
<view class="container">
  <view class="header">
    <text class="title">功能中心</text>
    <text class="welcome">欢迎，{{userInfo.name || '用户'}}</text>
  </view>
  
  <view class="features-container">
    <view class="feature-card" bindtap="onChatAnalysis">
      <view class="feature-icon">🤖</view>
      <view class="feature-content">
        <text class="feature-title">Chat模型分析</text>
        <text class="feature-desc">使用DeepSeek普通模型分析更快</text>
      </view>
      <view class="feature-arrow"></view>
    </view>
    
    <view class="feature-card" bindtap="onReasonerAnalysis">
      <view class="feature-icon">🧠</view>
      <view class="feature-content">
        <text class="feature-title">推理模型分析</text>
        <text class="feature-desc">使用DeepSeek推理模型分析更详细</text>
      </view>
      <view class="feature-arrow"></view>
    </view>
  </view>
  
  <view class="nav-buttons">
    <button class="nav-btn" bindtap="navigateToUserInfo">个人信息</button>
  </view>
</view>