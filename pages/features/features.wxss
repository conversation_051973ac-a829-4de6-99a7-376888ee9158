/* pages/features/features.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(0deg, #ffffff 0%, #EAD4FF 20%, #a175e7 100%);
  display: flex;
  flex-direction: column;
}

.header {
  margin-bottom: 40rpx;
  text-align: center;
}

.title {
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.welcome {
  font-size: 28rpx;
  color: #666;
}

.features-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.feature-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.feature-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
}

.feature-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 4rpx solid #999;
  border-right: 4rpx solid #999;
  transform: rotate(45deg);
}

.nav-buttons {
  margin-top: auto;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid #a175e7;
  color: #a175e7;
  border-radius: 10rpx;
  font-size: 28rpx;
}