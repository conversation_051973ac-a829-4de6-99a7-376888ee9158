// pages/features/features.js
const userUtils = require('../../utils/user-utils.js');

// 定义一个全局logger获取函数
function getLogger() {
  return getApp().globalData.logger;
}

// 防止重复提交标志
let isSubmitting = false;

// 添加安全提交处理函数
function safeguardSubmit(submitFunction) {
  return async function(...args) {
    if (isSubmitting) {
      wx.showToast({
        title: '请勿重复提交',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    isSubmitting = true;
    
    try {
      return await submitFunction.apply(this, args);
    } catch (error) {
      getLogger().error('表单提交错误:', error);
      throw error;
    } finally {
      // 延迟释放锁，防止意外的快速重复点击
      setTimeout(() => {
        isSubmitting = false;
      }, 1000);
    }
  };
}

Page({
  data: {
    userInfo: null,
    features: [
      { id: 'chat', name: 'Chat模型分析', desc: '使用DeepSeek普通模型分析更快' },
      { id: 'reasoner', name: '推理模型分析', desc: '使用DeepSeek推理模型分析更详细' }
    ]
  },
  
  onLoad: function() {
    // 保存logger引用到页面实例
    this.logger = getLogger();
    
    // 检查用户是否已登录
    const userInfo = userUtils.requireUserLogin();
    if (!userInfo) {
      return;
    }
    
    this.setData({ userInfo });
    
    // 使用安全提交包装
    this.safeChatAnalysis = safeguardSubmit(this.onChatAnalysis);
    this.safeReasonerAnalysis = safeguardSubmit(this.onReasonerAnalysis);
  },
  
  // Chat模型分析
  onChatAnalysis: function() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showToast({ title: '请先完善个人信息', icon: 'none' });
      return;
    }
    
    // 跳转到Chat模型流式渲染页面
    wx.navigateTo({
      url: '/pages/chat-stream/chat-stream?infoHash=' + userInfo.infoHash,
      success: () => { 
        this.logger.debug('成功跳转到Chat模型流式渲染页面');
      },
      fail: (error) => {
        this.logger.error('Chat模型流式渲染页面跳转失败，尝试跳转到传统页面:', error);
        
        // 如果新页面跳转失败，回退到使用旧的页面
        wx.navigateTo({
          url: '/pages/result/result',
          success: () => { 
            this.logger.debug('回退成功，跳转到传统结果页面');
          },
          fail: (fallbackError) => {
            this.logger.error('页面跳转失败（流式和传统页面均失败）:', fallbackError);
            wx.showToast({ 
              title: '页面跳转失败', 
              icon: 'none',
            });
          }
        });
      }
    });
  },
  
  // 推理模型分析
  onReasonerAnalysis: function() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showToast({ title: '请先完善个人信息', icon: 'none' });
      return;
    }
    
    // 跳转到推理模型流式渲染页面
    wx.navigateTo({
      url: '/pages/reasoner-stream/reasoner-stream',
      success: () => { 
        this.logger.debug('成功跳转到流式渲染页面');
      },
      fail: (error) => {
        this.logger.error('流式渲染页面跳转失败，尝试跳转到传统页面:', error);
        
        // 如果新页面跳转失败，回退到使用旧的页面
        wx.navigateTo({
          url: '/pages/simple-result/simple-result',
          success: () => { 
            this.logger.debug('回退成功，跳转到传统页面');
          },
          fail: (fallbackError) => {
            this.logger.error('页面跳转失败（流式和传统页面均失败）:', fallbackError);
            wx.showToast({ 
              title: '页面跳转失败', 
              icon: 'none',
            });
          }
        });
      }
    });
  },
  
  // 跳转到个人信息页
  navigateToUserInfo: function() {
    wx.navigateTo({
      url: '/pages/user-info/user-info'
    });
  }
})
