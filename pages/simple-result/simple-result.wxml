<!-- simple-result.wxml -->

<view class="container">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-box">
      <view class="loading-animation"></view>
      <text class="loading-text">{{analysisStep}}</text>
      <text class="loading-tip">推理模型分析需要较长时间，请耐心等待（约2-3分钟）</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error-box">
      <icon type="{{isDuplicateRequest ? 'info' : 'warn'}}" size="64" color="{{isDuplicateRequest ? '#a175e7' : '#e64340'}}"></icon>
      <text class="error-title" style="color: {{isDuplicateRequest ? '#a175e7' : '#e64340'}}">{{isDuplicateRequest ? '请求冲突' : '发生错误'}}</text>
      <text class="error-message">{{error}}</text>
      <button class="retry-button" bindtap="retryAnalysis">{{isDuplicateRequest ? '刷新页面' : '重新开始'}}</button>
    </view>
  </view>

  <!-- 结果显示 -->
  <view class="result-container" wx:else>
    <!-- 卡片1: 推理模型分析结果 -->
    <view class="result-card">
      <!-- 结果标题 -->
      <view class="result-header">
        <text class="result-title">推理模型分析结果</text>
      </view>
      
      <!-- 主要结果内容 -->
      <view class="result-content">
        <rich-text nodes="{{result}}" space="nbsp"></rich-text>
      </view>
      
      <!-- 内容分隔线 -->
      <view class="section-divider" wx:if="{{reasoningContent}}"></view>
      
      <!-- 推理过程标题 -->
      <view class="reasoning-header" wx:if="{{reasoningContent}}">
        <text class="reasoning-title">推理过程</text>
      </view>
      
      <!-- 推理过程内容 -->
      <view class="reasoning-content" wx:if="{{reasoningContent}}">
        <rich-text nodes="{{reasoningContent}}" space="nbsp"></rich-text>
      </view>
    </view>
    
    <!-- 卡片2: 付费解锁获取更详细的大运流年关键建议 -->
    <view class="premium-card">
      <view class="premium-header">
        <text class="premium-title">获取更详细的大运流年关键建议</text>
      </view>
      
      <view class="premium-actions">
        <button class="premium-unlock-btn" bindtap="navigateToSecondStage">
          付费解锁
        </button>
        
        <button class="back-button" bindtap="navigateBack">返回上一页</button>
      </view>
    </view>
  </view>
</view>
