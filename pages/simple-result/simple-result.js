Page({
  data: {
    isLoading: true,
    error: null,
    analysisStep: '正在分析中...',
    analysisTimer: null, // 分析步骤更新定时器
    analysisStartTime: 0, // 分析开始时间
    userInfo: null, // 用户信息
    hasSecondStage: false, // 检查是否有第二轮分析
    showMoreButton: false, // 显示"查看更多"按钮
    infoHash: '', // 保存当前分析的infoHash
    guideText: '', // 添加二阶段分析引导
    isDuplicateRequest: false, // 标记是否为重复请求错误
  },

  onLoad: function() {
    this.pageActive = true;
    this.currentRequest = null;
    this.isRequesting = false;  // 标记是否正在请求中
    
    // 获取全局日志工具
    this.logger = getApp().globalData.logger;
    
    // 导入配置
    this.config = require('../../config');
    
    wx.setNavigationBarTitle({
      title: '推理模型分析'
    });

    setTimeout(() => {
      if (this.pageActive) {
        this.loadUserDataAndStartAnalysis();
      }
    }, 200);
  },
  
  loadUserDataAndStartAnalysis: function() {
    // 如果已经在请求中，不要重复请求
    if (this.isRequesting) {
      return;
    }
    
    try {
      // 获取最新的用户信息
      const userInfo = wx.getStorageSync('userInfo');
      
      if (!userInfo) {
        this.handleError('未找到用户信息');
        return;
      }
  
      const requiredFields = ['year', 'month', 'day', 'gender', 'infoHash'];
      const missingFields = requiredFields.filter(field => !userInfo[field]);
      
      // 特别检查hour和minute，因为它们可能是0
      if (userInfo.hour === undefined || userInfo.hour === null) {
        missingFields.push('hour');
      }
      
      if (missingFields.length > 0) {
        this.logger.warn(`用户信息不完整: 缺少 ${missingFields.join(', ')}`);
        
        if (missingFields.includes('infoHash')) {
          this.handleError('用户信息中缺少必要的哈希值');
          return;
        }
      }
  
      this.setData({ userInfo });
      
      // 直接调用推理模型分析
      this.callReasonerModel(userInfo);
    } catch (err) {
      this.handleError(`读取用户数据出错: ${err.message}`);
    }
  },
  
  handleError: function(message) {
    this.logger.error(message);
    this.setData({
      isLoading: false,
      error: message,
      analysisStep: '分析失败'
    });
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
    
    setTimeout(() => {
      if (this.pageActive) {
        wx.navigateBack({
          delta: 1,
          fail: () => {
            wx.redirectTo({
              url: '/pages/input-info/input-info'
            });
          }
        });
      }
    }, 2000);
  },

  exitAnalysis: function() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.redirectTo({
          url: '/pages/input-info/input-info'
        });
      }
    });
  },

  // 格式化文本内容
  formatContent: function(text) {
    if (!text) return '';
    
    // 移除回车符
    text = text.replace(/\r/g, '');
    
    // 预处理标题和特殊格式
    let lines = text.split('\n');
    const processedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      let line = lines[i].trim();
      
      if (line === '') {
        processedLines.push('');
        continue;
      }
      
      // 处理标题，确保"#"和文本之间有空格
      if (line.match(/^#{1,6}[^#\s]/)) {
        line = line.replace(/^(#{1,6})([^#\s])/, '$1 $2');
      }
      
      // 规范化中文序号标题格式
      if (line.match(/^[一二三四五六七八九十]+[、：:]/)) {
        line = line.replace(/^([一二三四五六七八九十]+[、：:])(\S)/, '$1 $2');
      }
      
      processedLines.push(line);
    }
    
    // 重组文本并确保段落之间只有一个空行
    text = processedLines.join('\n');
    text = text.replace(/\n{3,}/g, '\n\n');
    
    return text;
  },

  // 转换markdown为HTML - 简化版本
  convertMarkdownToHtml: function(markdown) {
    if (!markdown) {
      return '';
    }

    try {
      let html = markdown;

      // 特别处理中文数字标题（一、二、三...）
      html = html.replace(/^([一二三四五六七八九十]+)[、：:](?:\s*)(.*?)$/gm, function(match, num, content) {
        const trimmedContent = content.trim();
        if (trimmedContent) {
          // 不添加伪元素，直接使用h3标签，这样可以利用h3的样式而不是自定义的chinese-title
          return `<h3 class="chinese-num-title">${num}、${trimmedContent}</h3>`;
        }
        return match;
      });

      // 简化的Markdown处理，只处理基本元素
      // 处理标题 - 从最高级别(6个#)开始处理，避免嵌套替换问题
      html = html.replace(/^###### (.*$)/gm, '<h6>$1</h6>');
      html = html.replace(/^##### (.*$)/gm, '<h5>$1</h5>');
      html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
      html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
      html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
      html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

      // 处理加粗和斜体
      html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

      // 处理换行和段落 - 优化版本
      const paragraphs = html.split(/\n\n+/);
      
      html = paragraphs.map(p => {
        // 跳过特殊标签
        if (p.trim() === '' || 
            p.match(/^<(div|h[1-6]|blockquote)/) || 
            p.match(/<div class="(chinese-title|bracket-title)">/) ||
            p.match(/<h3 class="chinese-num-title">/)) {
          return p;
        }
        return `<p>${p.replace(/\n/g, '<br>')}</p>`;
      }).join('\n\n');
      
      return html;
    } catch (error) {
      this.logger.error('Markdown处理出错:', error);
      // 出错时，直接返回纯文本，确保内容可读
      return `<p>${markdown.replace(/\n/g, '<br><br>')}</p>`;
    }
  },

  // 开始更新分析步骤
  startAnalysisStepUpdates: function() {
    this.setData({
      analysisStartTime: Date.now()
    });
    
    if (this.data.analysisTimer) {
      clearInterval(this.data.analysisTimer);
    }
    
    const timer = setInterval(() => {
      const elapsedSeconds = Math.floor((Date.now() - this.data.analysisStartTime) / 1000);
      let newStep = '';
      
      if (elapsedSeconds < 30) {
        newStep = '正在计算八字信息...';
      } else if (elapsedSeconds < 60) {
        newStep = '正在进行推理分析...';
      } else if (elapsedSeconds < 90) {
        newStep = '推理分析进行中，请耐心等待...';
      } else if (elapsedSeconds < 120) {
        newStep = '正在生成详细分析结果...';
      } else {
        newStep = `分析仍在进行中，已用时${Math.floor(elapsedSeconds/60)}分${elapsedSeconds%60}秒...`;
      }
      
      this.setData({ analysisStep: newStep });
    }, 5000);
    
    this.setData({ analysisTimer: timer });
  },
  
  // 停止更新分析步骤
  stopAnalysisStepUpdates: function() {
    if (this.data.analysisTimer) {
      clearInterval(this.data.analysisTimer);
      this.setData({ analysisTimer: null });
    }
  },

  // 跳转到二阶段结果页
  navigateToSecondStage: function() {
    if (!this.data.infoHash) {
      wx.showToast({
        title: '缺少必要的infoHash参数',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/secondStage-result/secondStage-result?infoHash=${this.data.infoHash}`,
      fail: (err) => {
        this.logger.error('跳转到二阶段结果页失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  
  // 重新开始分析
  retryAnalysis: function() {
    // 重置页面状态
    this.setData({
      isLoading: true,
      error: null,
      analysisStep: '正在分析中...',
      result: '',
      reasoningContent: '',
      isDuplicateRequest: false // 重置重复请求标记
    });
    
    // 停止之前的分析步骤更新
    this.stopAnalysisStepUpdates();
    
    // 重新开始分析流程
    this.loadUserDataAndStartAnalysis();
  },
  
  // 调用推理模型
  callReasonerModel: function(userInfo) {
    if (!this.pageActive) {
      this.logger.debug('页面不活跃，跳过请求');
      return;
    }
    
    if (this.isRequesting) {
      this.logger.debug('已有请求正在进行中，跳过');
      return;
    }
    
    // 确保用户信息中存在 infoHash
    if (!userInfo.infoHash) {
      this.logger.error('用户信息中缺少 infoHash，无法发起请求');
      this.handleError('用户信息不完整');
      return;
    }
    
    const infoHash = userInfo.infoHash;
    
    this.isRequesting = true;
    
    // 设置加载状态
    this.setData({ 
      isLoading: true, 
      error: null,
      analysisStep: '正在准备分析...',
      result: '',
      reasoningContent: '',
      showMoreButton: false,
      infoHash: infoHash  // 保存到页面数据中
    });
    
    // 开始更新分析步骤
    this.startAnalysisStepUpdates();
    
    // 准备请求数据 - 移除敏感字段日志输出
    const data = {
      year: String(userInfo.year),
      month: String(userInfo.month),
      day: String(userInfo.day),
      hour: String(userInfo.hour),
      minute: String(userInfo.minute || 0),
      location: String(userInfo.location || ''),
      gender: String(userInfo.gender),
      infoHash: infoHash,  // 使用 infoHash 作为主键
      submissionId: userInfo.submissionId,
      clientTimestamp: Date.now()
    };
    
    // 添加经度信息（如果存在）
    if (userInfo.longitude) {
      data.longitude = String(userInfo.longitude);
    }
    
    this.logger.info('发送推理请求');
    this.logger.debug('请求数据', data);

    // 从配置中获取API基础URL
    const currentEnv = this.config.CURRENT_ENV || 'development';
    const apiBaseUrl = this.config.API_ENV[currentEnv];
    this.logger.debug('使用API服务器地址:', apiBaseUrl);

    this.currentRequest = wx.request({
      url: `${apiBaseUrl}/api/v1/reasoner`,
      method: 'POST',
      header: { 'Content-Type': 'application/json' },
      data,
      timeout: 300000,
      success: (res) => {
        if (!this.pageActive) {
          this.logger.debug('页面已不活跃，忽略请求结果');
          return;
        }
        
        this.logger.debug('推理请求响应状态:', res.statusCode);
        this.stopAnalysisStepUpdates();
        
        if (res.statusCode === 200) {
          if (res.data && res.data.code === 200) {
            // 获取响应数据 - 注意这里要从data字段中获取
            const responseData = res.data.data || {};
            const { content, reasoningContent, hasSecondStage } = responseData;
            
            // 格式化内容
            const htmlContent = this.convertMarkdownToHtml(this.formatContent(content || ''));
            const htmlReasoning = this.convertMarkdownToHtml(this.formatContent(reasoningContent || ''));
            
            // 更新界面
            this.setData({
              isLoading: false,
              result: htmlContent,
              reasoningContent: htmlReasoning,
              analysisStep: '分析完成',
              hasSecondStage: !!hasSecondStage,
              showMoreButton: !!hasSecondStage,
              infoHash: infoHash  // 直接使用客户端生成的infoHash
            });

            // 在结果最后添加引导文本
            if (hasSecondStage) {
              this.appendSecondStageGuide();
            }
            this.logger.info('分析完成，更新界面');
          } else if (res.data && res.data.code === 40001) {
            // 处理重复请求错误
            this.setData({
              isLoading: false,
              error: '系统已经在拼命分析中，请稍后点击刷新按钮查看结果。',
              analysisStep: '请求已存在',
              isDuplicateRequest: true // 标记是重复请求错误
            });
          } else {
            // 处理其他服务器返回的错误
            const errorMsg = res.data?.message || res.data?.error || '未知错误';
            this.logger.error('服务器返回错误:', errorMsg);
            this.setData({
              isLoading: false,
              error: `服务器返回错误: ${errorMsg}`,
              analysisStep: '分析失败'
            });
          }
        } else {
          // 处理HTTP错误
          this.logger.error('HTTP错误:', res.statusCode);
          this.setData({
            isLoading: false,
            error: `服务器返回HTTP错误: ${res.statusCode}`,
            analysisStep: '分析失败'
          });
        }
      },
      fail: (error) => {
        if (!this.pageActive) {
          this.logger.debug('页面已不活跃，忽略请求失败');
          return;
        }
        
        if (error.errMsg && error.errMsg.indexOf('abort') !== -1) {
          this.logger.debug('请求被中止');
          return;
        }
        
        this.logger.error('推理请求失败:', error.errMsg);
        this.stopAnalysisStepUpdates();
        
        this.setData({
          isLoading: false,
          error: error.errMsg || '网络请求失败',
          analysisStep: '分析失败'
        });
      },
      complete: () => {
        this.logger.debug('推理请求完成，重置请求状态');
        if (this.pageActive) {
          this.currentRequest = null;
          this.isRequesting = false;
        }
      }
    });
  },
  
  // 设置二阶段分析引导
  appendSecondStageGuide: function() {
    if (!this.data.result) return;
    
    this.setData({
      guideText: '✨ 付费解锁获取更详细的大运流年关键建议 ✨'
    });
  },

  // 页面卸载时清理资源
  onUnload: function() {
    this.logger.debug('页面卸载，清理资源');
    this.pageActive = false;
    this.isRequesting = false;
    
    this.stopAnalysisStepUpdates();
    
    if (this.currentRequest) {
      this.logger.debug('中止当前请求');
      this.currentRequest.abort();
      this.currentRequest = null;
    }
  },

  navigateBack: function() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        // 如果无法返回上一页（例如，页面是通过分享打开的），则跳转到首页
        wx.redirectTo({
          url: '/pages/index/index'
        });
      }
    });
  },
}); 