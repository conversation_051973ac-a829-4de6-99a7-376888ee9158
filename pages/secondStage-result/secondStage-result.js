Page({
  data: {
    isLoading: true,
    error: null,
    loadingText: '正在获取分析结果...',
    pendingMessage: '',
    secondStageContent: '',
    infoHash: '',
    retryCount: 0
  },

  onLoad: function(options) {
    this.pageActive = true;
    this.isRequesting = false;
    
    // 获取全局日志工具
    this.logger = getApp().globalData.logger;
    
    // 导入配置
    this.config = require('../../config');
    
    // 获取infoHash参数
    const infoHash = options.infoHash;
    
    if (!infoHash) {
      this.handleError('缺少必要的infoHash参数');
      return;
    }
    
    this.setData({ infoHash });
    
    // 开始获取分析结果
    this.fetchSecondStageAnalysis(infoHash);
  },
  
  // 获取第二轮分析结果
  fetchSecondStageAnalysis: function(infoHash) {
    // 基本验证
    if (!this.pageActive) {
      this.logger.debug('页面不活跃，跳过请求');
      return;
    }
    
    if (this.isRequesting) {
      this.logger.debug('已有请求正在进行中，跳过');
      return;
    }
    
    this.isRequesting = true;
    
    // 设置加载状态
    this.setData({
      isLoading: true,
      error: null,
      loadingText: '正在获取分析结果...',
      pendingMessage: ''
    });
    
    // 从配置中获取API基础URL
    const currentEnv = this.config.CURRENT_ENV || 'development';
    const apiBaseUrl = this.config.API_ENV[currentEnv];
    this.logger.debug('使用API服务器地址:', apiBaseUrl);
    
    // 发送请求到second-stage接口
    wx.request({
      url: `${apiBaseUrl}/api/v1/reasoner/second-stage`,
      method: 'POST',
      header: {'Content-Type': 'application/json'},
      data: { infoHash: infoHash },
      timeout: 30000,
      
      success: (res) => {
        if (!this.pageActive) return;
        
        this.logger.debug('第二轮分析响应状态:', res.statusCode);
        
        // 请求成功，处理结果
        if (res.statusCode === 200 && res.data.success) {
          const { secondStage, pending } = res.data;
          
          // 处理不同状态
          if (pending || (secondStage && secondStage.pending)) {
            // 分析仍在进行中
            const waitMessage = secondStage?.message || '分析正在进行中，请耐心等待';
            const estimatedTime = secondStage?.estimatedCompletion || '约5-10分钟';
            
            this.logger.info('分析仍在进行中，设置自动轮询');
            this.setData({
              isLoading: true,
              loadingText: '等待分析完成...',
              pendingMessage: waitMessage
            });
            
            // 自动轮询检查状态
            setTimeout(() => {
              if (this.pageActive) {
                this.retryCount = (this.data.retryCount || 0) + 1;
                this.setData({ retryCount: this.retryCount });
                this.fetchSecondStageAnalysis(infoHash);
              }
            }, 15000); // 15秒后自动重试
          } else if (secondStage?.content) {
            // 分析完成，显示结果
            this.logger.info('第二轮分析完成，处理结果');
            const htmlContent = this.convertMarkdownToHtml(this.formatContent(secondStage.content));
            this.setData({
              isLoading: false,
              secondStageContent: htmlContent
            });
          } else {
            // 异常情况
            this.handleError('获取到的分析结果格式不正确');
          }
        } else {
          // 请求失败
          const errorMessage = res.data?.message || '获取分析失败';
          this.handleError(errorMessage);
        }
      },
      
      fail: (error) => {
        if (!this.pageActive) return;
        
        this.logger.error('第二轮分析请求失败:', error.errMsg);
        this.handleError('网络请求失败: ' + error.errMsg);
      },
      
      complete: () => {
        this.isRequesting = false;
      }
    });
  },
  
  // 处理错误
  handleError: function(message) {
    this.logger.error(message);
    this.setData({
      isLoading: false,
      error: message
    });
  },
  
  // 手动刷新分析
  refreshAnalysis: function() {
    if (this.data.infoHash) {
      this.fetchSecondStageAnalysis(this.data.infoHash);
    } else {
      this.handleError('缺少必要的infoHash参数');
    }
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        // 如果无法返回，则跳转到入口页面
        wx.redirectTo({
          url: '/pages/input-info/input-info'
        });
      }
    });
  },
  
  // 格式化文本内容
  formatContent: function(text) {
    if (!text) return '';
    // 仅移除回车符，保留换行符和空白字符的原始格式
    return text.replace(/\r/g, '');
  },
  
  // 转换markdown为HTML - 简化版本
  convertMarkdownToHtml: function(markdown) {
    if (!markdown) {
      return '';
    }

    try {
      let html = markdown;

      // 移除标题符号(#)而非转换为HTML标题
      html = html.replace(/^#{1,6}\s+(.*$)/gm, '$1');

      // 处理加粗和斜体
      html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

      // 处理换行 - 保持原始换行格式
      html = html.replace(/\n/g, '<br>');
      
      // 包装段落，但保持原始格式
      html = '<p>' + html + '</p>';
      // 修复连续换行导致的多余标签
      html = html.replace(/<br><br>/g, '</p><p>');
      
      return html;
    } catch (error) {
      this.logger.error('Markdown处理出错:', error);
      // 出错时，直接返回纯文本，确保内容可读
      return `<p>${markdown.replace(/\n/g, '<br>')}</p>`;
    }
  },
  
  // 页面卸载
  onUnload: function() {
    this.logger.debug('页面卸载');
    this.pageActive = false;
    this.isRequesting = false;
  }
}); 