<view class="container">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-animation"></view>
    <text class="loading-text">{{loadingText}}</text>
    <text class="loading-tip" wx:if="{{pendingMessage}}">{{pendingMessage}}</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error-box">
      <icon type="warn" size="64" color="#e64340"></icon>
      <text class="error-title">发生错误</text>
      <text class="error-message">{{error}}</text>
      <button class="retry-button" bindtap="refreshAnalysis">重试</button>
      <button class="back-button" bindtap="navigateBack">返回</button>
    </view>
  </view>

  <!-- 结果显示 -->
  <view class="result-container" wx:else>
    <view class="result-header">
      <text class="result-title">大运流年关键建议</text>
    </view>
    
    <view class="result-content">
      <rich-text nodes="{{secondStageContent}}" space="nbsp"></rich-text>
    </view>
    
    <button class="back-button" bindtap="navigateBack">返回</button>
  </view>
</view> 