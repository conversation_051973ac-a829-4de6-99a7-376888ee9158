// pages/chat-stream/chat-stream.js
// 统一后的 Chat 模型流式页面 —— 仅依赖 stream‑render‑behavior 转发的
// analysisChunk / analysisResult 事件，不再直接解析原始 WebSocket 消息。

const streamRenderBehavior = require('../../behaviors/stream-render-behavior');

Page({
  /** 行为 */
  behaviors: [streamRenderBehavior],

  /** 页面状态 */
  data: {
    isLoading: false,
    error: null,
    activeTab: 'result',  // 默认展示结果
    infoHash: '',
    isStreaming: false
  },

  // =============== 事件回调 =================
  /**
   * 由 stream‑render‑behavior 在接收完整结果后调用
   * @param {{data:{content:string}}} payload
   */
  _handleBasicAnalysis({ data }) {
    if (!data || !data.content) return;

    // 可选：统一格式化／Markdown → HTML
    const formatted = this.formatContent ? this.formatContent(data.content) : data.content;
    const html       = this.convertMarkdownToHtml ? this.convertMarkdownToHtml(formatted) : formatted;

    this.setData({
      result: html,
      content: data.content,
      isLoading: false,
      isStreaming: false
    });

    this.logger.info('已显示完整 Chat 分析结果');
  },

  // =============== 生命周期 =================
  onLoad(options) {
    this.logger = getApp().globalData.logger;
    this.config = require('../../config');

    const { infoHash } = options || {};
    if (!infoHash) {
      this._handleError('缺少必要的 infoHash 参数');
      return;
    }

    this.setData({ infoHash });

    // 延迟到下一个 tick，确保全局 socketClient 已准备好（由行为初始化）
    wx.nextTick(() => {
      this._startChatAnalysis();
    });
  },

  onUnload() {
    // 交给 behavior 做通用的清理，如 off()、心跳、timer 等
  },

  // =============== 业务函数 =================
  async _startChatAnalysis() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo) throw new Error('未找到用户信息');

      userInfo.infoHash = this.data.infoHash;

      // 由 socket-client（全局单例，behaviors 内注入）发送启动指令
      await this.socketClient.send({
        type: 'startChatAnalysis',
        data: userInfo
      });

      this.setData({ isLoading: true, isStreaming: true, result: '', content: '' });
    } catch (e) {
      this._handleError(e.message || '启动分析失败');
    }
  },

  retryAnalysis() {
    if (!this.data.infoHash) return;
    this.setData({ error: null, result: '', content: '' });
    this._startChatAnalysis();
  },

  navigateBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.redirectTo({ url: '/pages/input-info/input-info' });
      }
    });
  },

  // =============== 工具 =================
  _handleError(msg) {
    this.logger.error(msg);
    this.setData({ isLoading: false, error: msg });
    wx.showToast({ title: msg, icon: 'none', duration: 2000 });
  }
});
