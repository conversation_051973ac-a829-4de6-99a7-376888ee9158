/* pages/chat-stream/chat-stream.wxss */

/* 页面主容器 */
.container {
  padding: var(--spacing-200);
  min-height: 100vh;
  background: linear-gradient(0deg, #ffffff 0%, #EAD4FF 20%, #a175e7 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  box-sizing: border-box;
  position: relative;
}

/* 加载中状态 */
.loading-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; 
  margin-top: var(--spacing-200);
  min-height: 80vh;
}

.loading-box { 
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-300);
  box-sizing: border-box;
  background-color: #fff;
  border-radius: var(--radius-medium);  
  box-shadow: 0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
  border: 1rpx solid var(--color-bluepurple-light4);
}

.loading-animation {
  width: 120rpx;
  height: 120rpx;
  border: 8rpx solid var(--color-bluepurple-light3);
  border-top: 8rpx solid var(--color-bluepurple);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
  margin-bottom: var(--spacing-300);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-medium);
  color: var(--color-bluepurple);
  text-align: center;
  font-weight: 500;
  margin-bottom: var(--spacing-200);
  width: 100%;
}

.loading-tip {
  font-size: var(--font-size-normal);
  color: var(--color-bluegray-dark5);
  text-align: center;
  margin-top: var(--spacing-100);
  padding: 0 var(--spacing-200);
  line-height: 1.5;
  width: 100%;
}

/* 错误状态 */
.error-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-200);
  min-height: 80vh;
}

.error-box {
  width:100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-300);
  box-sizing: border-box;
  background-color: #fff;
  border-radius: var(--radius-medium);
  box-shadow:0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
  border: 1rpx solid var(--color-bluepurple-light4);
}

.error-title {
  font-size: var(--font-size-large);
  font-weight: bold;
  margin: var(--spacing-200) 0;
}

.error-message {
  font-size: var(--font-size-normal);
  color: var(--color-bluegray-dark5);
  text-align: center;
  margin-bottom: var(--spacing-300);
  line-height: 1.6;
}

.retry-button {
  background-color: var(--color-bluepurple-light2);
  border-radius: var(--radius-large);
  min-width: 60%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-top: var(--spacing-200);
  box-shadow: 0rpx 4rpx 6rpx var(--color-bluegray), 0rpx 8rpx 16rpx var(--color-bluegray-light1);                
  font-size: var(--font-size-medium);
  font-weight: bold;
  color: var(--color-bluegray-dark5);
}

/* 结果容器 */
.result-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-300);
  margin-top: var(--spacing-200);
  margin-bottom: var(--spacing-300);
}

/* 保留loading-animation.small样式因为其他页面仍在使用 */
.loading-animation.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
  margin-bottom: 0;
}

/* 内容顺序设计样式 */
.sequential-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-300);
}

.content-section {
  width: 100%;
  background-color: #fff;
  border-radius: var(--radius-medium);
  border: 1rpx solid var(--color-bluepurple-light4);
  box-shadow: 0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
  overflow: hidden;
  margin-bottom: var(--spacing-200);
}

.section-header {
  padding: var(--spacing-200);
  background-color: var(--color-bluepurple-light3);
  border-bottom: 1rpx solid var(--color-bluepurple-light4);
}

.section-title {
  font-size: var(--font-size-medium);
  font-weight: bold;
  color: var(--color-bluepurple-dark1);
}

.section-body {
  padding: var(--spacing-300);
}

/* 结论部分特殊样式 */
.conclusion-section {
  background-color: #fff;
}

.conclusion-section .section-body {
  min-height: 600rpx;
}

/* 按钮样式 */
.action-buttons {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-300);
  margin-bottom: var(--spacing-300);
}

.back-button {
  background: linear-gradient(135deg, #8c63e3 0%, #6236bc 100%);
  color: white;
  font-weight: bold;
  font-size: var(--font-size-medium);
  border-radius: var(--radius-large);
  width: 80%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(98, 54, 188, 0.3);
}

/* 富文本内容样式 */
.result-content {
  font-size: var(--font-size-normal);
  line-height: 1.8;
  color: var(--color-bluegray-dark5);
}

/* 富文本通用样式 */
rich-text {
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-all;
}

rich-text .custom-title {
  font-weight: bold;
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  color: var(--color-bluepurple-dark1);
}

rich-text h1, rich-text h2, rich-text h3, rich-text h4, rich-text h5, rich-text h6 {
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  color: var(--color-bluepurple-dark1);
}

rich-text p {
  margin-bottom: 16rpx;
} 