<!--pages/chat-stream/chat-stream.wxml-->
<view class="container">
  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{error}}">
    <view class="error-box">
      <icon type="{{isDuplicateRequest ? 'info' : 'warn'}}" size="64" color="{{isDuplicateRequest ? '#a175e7' : '#e64340'}}"></icon>
      <text class="error-title" style="color: {{isDuplicateRequest ? '#a175e7' : '#e64340'}}">{{isDuplicateRequest ? '请求冲突' : '发生错误'}}</text>
      <text class="error-message">{{error}}</text>
      <button class="retry-button" bindtap="retryAnalysis">{{isDuplicateRequest ? '刷新页面' : '重新开始'}}</button>
    </view>
  </view>

  <!-- 结果显示（包括流式渲染状态） -->
  <view class="result-container" wx:else>
    <!-- 主要内容部分 -->
    <view class="sequential-content">
      <!-- 内容部分 -->
      <view class="content-section conclusion-section">
        <view class="section-header">
          <text class="section-title">八字命盘分析</text>
        </view>
        <view class="section-body result-content">
          <rich-text nodes="{{result}}" space="nbsp"></rich-text>
        </view>
      </view>
    </view>
    
    <!-- 返回按钮 -->
    <view class="action-buttons">
      <button class="back-button" bindtap="navigateBack">返回</button>
    </view>
  </view>
</view> 