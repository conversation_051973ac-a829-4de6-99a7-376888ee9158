// pages/user-info/user-info.js
const config = require('../../config.js');
const userUtils = require('../../utils/user-utils.js');

Page({
  data: {
    userInfo: null,
    isLoading: false
  },

  onLoad: function (options) {
    console.log('[UserInfo] 页面加载');

    // 检查用户是否已登录
    const userInfo = userUtils.requireUserLogin();
    if (!userInfo) {
      return;
    }

    this.setData({
      userInfo: userInfo
    });
  },

  // 获取API基础URL
  getApiBaseUrl: function() {
    const currentEnv = config.CURRENT_ENV;
    return config.API_ENV[currentEnv];
  },

  // 退出登录
  handleLogout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          userUtils.handleLogout();
        }
      }
    });
  },

  // 跳转到功能页
  navigateToFeatures: function() {
    wx.navigateTo({
      url: '/pages/features/features'
    });
  },

  // 跳转到信息填写页
  navigateToInputInfo: function() {
    wx.navigateTo({
      url: '/pages/input-info/input-info'
    });
  }
});
