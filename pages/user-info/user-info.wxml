<!--pages/user-info/user-info.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">个人信息</text>
  </view>

  <!-- 用户信息展示 -->
  <view class="user-container" wx:if="{{userInfo}}">
    <view class="avatar-container">
      <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"></image>
    </view>
    
    <view class="info-section">
      <view class="info-item">
        <text class="label">姓名</text>
        <text class="value">{{userInfo.name || '未设置'}}</text>
      </view>
      
      <view class="info-item">
        <text class="label">性别</text>
        <text class="value">{{userInfo.gender || '未设置'}}</text>
      </view>
      
      <view class="info-item">
        <text class="label">出生日期</text>
        <text class="value">{{userInfo.birthDate || '未设置'}}</text>
      </view>
      
      <view class="info-item">
        <text class="label">出生时间</text>
        <text class="value">{{userInfo.birthTime || '未设置'}}</text>
      </view>
      
      <view class="info-item">
        <text class="label">出生地点</text>
        <text class="value">{{userInfo.birthPlace || '未设置'}}</text>
      </view>
      
      <view class="info-item" wx:if="{{userInfo.occupation}}">
        <text class="label">职业</text>
        <text class="value">{{userInfo.occupation}}</text>
      </view>
      
      <view class="info-item" wx:if="{{userInfo.hobbies}}">
        <text class="label">兴趣爱好</text>
        <text class="value">{{userInfo.hobbies}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-group">
    <button class="btn-primary" bindtap="navigateToFeatures">进入功能页</button>
    <button class="btn-secondary" bindtap="navigateToInputInfo">修改信息</button>
    <button class="btn-logout" bindtap="handleLogout">退出登录</button>
  </view>
</view>
