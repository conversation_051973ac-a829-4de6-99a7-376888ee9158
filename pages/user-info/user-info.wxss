/* pages/user-info/user-info.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding-top: 20rpx;
}

.title {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.form-container {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section {
  margin-bottom: 50rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #e0e0e0;
}

.input-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.input:focus {
  border-color: #667eea;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.textarea:focus {
  border-color: #667eea;
}

.picker-display {
  height: 80rpx;
  line-height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background: white;
}

.picker-display:empty::before {
  content: attr(placeholder);
  color: #999;
}

.radio-group {
  display: flex;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.radio-item radio {
  margin-right: 12rpx;
}

.input-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  line-height: 1.4;
}

.btn-submit {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-submit[disabled] {
  opacity: 0.6;
}

.tips {
  text-align: center;
  padding: 20rpx;
}

.tip-text {
  display: block;
  color: #666;
  font-size: 24rpx;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 30rpx;
  }
  
  .form-container {
    padding: 30rpx;
  }
  
  .section {
    margin-bottom: 40rpx;
  }
}
