<view class="container">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-box">
      <view class="loading-animation"></view>
      <text class="loading-text">{{analysisStep}}</text>
      <text class="loading-tip">分析需要一点时间，请耐心等待...</text>
    </view>
  </view>

  <!-- 处理中状态 -->
  <view class="processing-container" wx:elif="{{isProcessing}}">
    <view class="error-box">
      <icon type="info" size="64" color="#07c160"></icon>
      <text class="error-title">分析进行中</text>
      <text class="error-message">您的分析请求正在处理中，请稍后刷新查看结果</text>
      <button class="retry-button" bindtap="refreshResult">刷新结果</button>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error-box">
      <icon type="warn" size="64" color="#e64340"></icon>
      <text class="error-title">发生错误</text>
      <text class="error-message">{{error}}</text>
      <button class="retry-button" bindtap="retryAnalysis">重新开始</button>
    </view>
  </view>

  <!-- 结果显示 -->
  <view class="result-container" wx:elif="{{showResult}}">
    <view class="result-card">
      <view class="result-header">
        <text class="result-title">chat模型分析结果</text>
      </view>
      <view class="result-content">
        <!-- 内容可能需要分段显示，每段之间添加分隔线 -->
        <rich-text nodes="{{formattedResult}}" space="nbsp"></rich-text>
        <!-- 分隔线 -->
        <view class="section-divider"></view>
      </view>
    </view>
    
    <!-- 添加返回按钮 -->
    <view class="back-button-container">
      <button class="back-button" bindtap="retryAnalysis">重新测试</button>
    </view>
  </view>
</view>
