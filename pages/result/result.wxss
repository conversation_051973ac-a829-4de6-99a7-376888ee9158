/* resilt.wxss */

/* 页面主容器 */
.container {
    padding: var(--spacing-200);
    min-height: 100vh;
    background: linear-gradient(0deg, #ffffff 0%, #EAD4FF 20%, #a175e7 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    position: relative;
  }
  
  /* 加载中样式 */
  .loading-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 20rpx;
    min-height: 80vh;
  }

  .loading-box { 
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-300); /* 内边距 */
    box-sizing: border-box; /* 确保padding不会增加元素总宽度 */
    background-color: #fff;
    border-radius: var(--radius-medium);  
    box-shadow: 0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
    border: 1rpx solid var(--color-bluepurple-light4);
  }

  .loading-animation {
    width: 120rpx;
    height: 120rpx;
    border: 8rpx solid var(--color-bluepurple-light3);
    border-top: 8rpx solid var(--color-bluepurple);
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
    margin-bottom: var(--spacing-300);
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .loading-text {
    font-size: var(--font-size-medium);
    color: var(--color-bluepurple);
    text-align: center;
    font-weight: 500;
    margin-bottom: var(--spacing-200);
    width: 100%; /* 确保文本宽度撑开容器 */
  }
  
  .loading-tip {
    font-size: var(--font-size-normal);
    color: var(--color-bluegray-dark5);
    text-align: center;
    margin-top: var(--spacing-100);
    padding: 0 var(--spacing-200);
    line-height: 1.5;
    width: 100%; /* 确保文本宽度撑开容器 */
  }
  
  /* 处理中样式和错误样式共用组件 */
  .processing-container, .error-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 20rpx;
    min-height: 80vh;
  }
  
  .error-box {
    width:100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-300); /* 添加内边距 */
    box-sizing: border-box; /* 确保padding不会增加元素总宽度 */
    background-color: #fff;
    border-radius: var(--radius-medium);
    box-shadow:0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
    border: 1rpx solid var(--color-bluepurple-light4);
  }
  
  .error-title {
    font-size: var(--font-size-large);
    font-weight: bold;
    margin: var(--spacing-200) 0;
  }
  
  .processing-container .error-title {
    color: #a175e7;
  }
  
  .error-container .error-title {
    color: #e64340;
  }
  
  .error-message {
    font-size: var(--font-size-normal);
    color: var(--color-bluegray-dark5);
    text-align: center;
    margin-bottom: var(--spacing-300);
    line-height: 1.6;
  }
  
  .retry-button {
    background-color: var(--color-bluepurple-light2);
    border-radius: var(--radius-large);
    min-width: 60%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-top: var(--spacing-200);
    box-shadow: 0rpx 4rpx 6rpx var(--color-bluegray), 0rpx 8rpx 16rpx var(--color-bluegray-light1); /*柔和优雅的双层阴影,普通按钮*/                 
    font-size: var(--font-size-medium);
    font-weight: bold;
    color: var(--color-bluegray-dark5);
  }
  
  /* 结果容器 */
  .result-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-300);
    margin-top: var(--spacing-200);
    margin-bottom: var(--spacing-300);
  }
  
 /* 结果卡片 */
 .result-card {
    width: 100%;
    background-color: #fff;
    border-radius: var(--radius-medium);
    border: 1rpx solid var(--color-bluepurple-light4);
    box-shadow: 0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
    overflow: hidden;
  }
  
  /* 结果头部 */
  .result-header {
    padding: var(--spacing-200);
    background: linear-gradient(135deg, var(--color-purple-dark1), var(--color-purple));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .result-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: rgba(255, 255, 255, 0.5);
  }
  
  .result-header::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
  }
   
  .result-title {
    font-size: var(--font-size-large);
    color: #fff;
    font-weight: bold;
    letter-spacing: 2rpx;
  }
  
 /* 结果内容 - 增加内边距提升可读性 */
 .result-content {
    padding: var(--spacing-400) var(--spacing-400); /* 增加内边距 */
    min-height: min-content;
    background-color: #fff;
    background-image: linear-gradient(to bottom, rgba(161, 117, 231, 0.02), rgba(161, 117, 231, 0.01));
    border-radius: 0 0 var(--radius-medium) var(--radius-medium);
  }
  
  /* 内容分隔线 */
  .section-divider {
    height: 2rpx;
    background: linear-gradient(to right, transparent, var(--color-bluepurple-light2), transparent);
    margin: var(--spacing-400) 0; /* 增加上下间距 */
    position: relative;
  }
  
  .section-divider::before {
    content: "✦";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 0 var(--spacing-100);
    color: var(--color-bluepurple);
    font-size: var(--font-size-small);
  }
  
  /* 返回按钮样式 */
  .back-button-container {
    padding: 30rpx;
    display: flex;
    justify-content: center;
  }
  
  .back-button {
    width: 80%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-large);
    background-color: var(--color-bluepurple-light2);
    box-shadow:  0rpx 4rpx 6rpx var(--color-bluegray), 0rpx 8rpx 16rpx var(--color-bluegray-light1);
    color:  var(--color-bluegray-dark5);
    font-size: var(--font-size-medium);
    font-weight: bold;
  }
   
  /* rich-text 内部元素样式 */
  .result-content rich-text {
    font-size: 28rpx;
    line-height: 1.66;
    color: #333;
    text-align: justify;
    word-break: break-word;
    letter-spacing: 0.5rpx;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
    display: block;
    white-space: pre-wrap;
    padding: 10rpx;
  }
  
  /* 标题样式 */
  .result-content rich-text h1 {
    font-size: 36rpx;
    font-weight: bold;
    margin: 10rpx 0 5rpx;
    color: #a175e7;
    border-bottom: 3rpx solid rgba(161, 117, 231, 0.3);
    padding-bottom: 12rpx;
    line-height: 1.8;
    text-align: left;
    clear: both;
  }
  
  .result-content rich-text h2 {
    font-size: 34rpx;
    font-weight: bold;
    margin: 10rpx 0 5rpx;
    color: #a175e7;
    line-height: 1.8;
    text-align: left;
    clear: both;
  }
  
  .result-content rich-text h3 {
    font-size: 32rpx;
    font-weight: bold;
    margin: 10rpx 0 5rpx;
    color: #a175e7;
    line-height: 1.8;
    text-align: left;
    clear: both;
    padding-left: 20rpx;
  }
  
  .result-content rich-text h4 {
    font-size: 30rpx;
    font-weight: bold;
    margin: 10rpx 0 5rpx;
    color: #333;
    line-height: 1.8;
    text-align: left;
    border-bottom: 1px dashed rgba(161, 117, 231, 0.3);
    clear: both;
    padding-left: 20rpx;
  }
  
  .result-content rich-text h5,
  .result-content rich-text h6 {
    font-size: 28rpx;
    font-weight: bold;
    margin: 28rpx 0 14rpx;
    color: #555;
    line-height: 1.8;
    text-align: left;
  }
  
  /* 段落样式 */
  .result-content rich-text p {
    margin-bottom: 10rpx;
    line-height: 1.8;
    text-indent: 2em;
    position: relative;
    display: block;
    white-space: pre-wrap;
    text-align: left;
  }
  
  /* 特别处理标题后的段落，减少间距 */
  .result-content rich-text h1 + p,
  .result-content rich-text h2 + p,
  .result-content rich-text h3 + p,
  .result-content rich-text h4 + p {
    margin-top: 0;
  }
  
  
  /* 强调样式 */
  .result-content rich-text strong {
    font-weight: bold;
  }
  
  /* 行内【】标记的样式 */
  .result-content rich-text .bracket-inline {
    color: #a175e7;
    font-weight: bold;
    font-size: 36rpx;
  }
  
  /* 中文序号标题样式 */
  .result-content rich-text .chinese-title {
    font-size: 34rpx;
    font-weight: bold;
    color: #a175e7;
    margin: 40rpx 0 30rpx;
    padding: 16rpx 0 16rpx 20rpx;
    display: block;
    line-height: 1.6;
    border-bottom: 1px solid #f0f0f0;
    border-left: 6rpx solid #a175e7;
    position: relative;
  }
  
  