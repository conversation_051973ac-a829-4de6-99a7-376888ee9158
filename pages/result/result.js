Page({
  data: {
    analysisResult: '',
    formattedResult: '',
    isLoading: true,
    showResult: false,
    error: null,
    basicAnalysis: '',
    analysisStep: '正在准备数据...',
    isProcessing: false,
    userInfo: null
  },

  onLoad: function(options) {
    // 获取全局日志工具
    this.logger = getApp().globalData.logger;
    
    // 导入配置
    this.config = require('../../config');
    
    // 从缓存获取用户输入信息
    const userInfo = wx.getStorageSync('userInfo');
    this.logger.debug('获取到的用户信息', { 
      year: userInfo?.year,
      month: userInfo?.month,
      day: userInfo?.day,
      gender: userInfo?.gender,
      location: userInfo?.location
    });
    
    if (!userInfo) {
      wx.showToast({
        title: '请先输入信息',
        icon: 'none'
      });
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/input-info/input-info'
        });
      }, 1500);
      return;
    }

    // 验证用户信息的完整性
    const requiredFields = ['year', 'month', 'day', 'location', 'gender'];
    const missingFields = requiredFields.filter(field => !userInfo[field]);
    
    // 特别检查hour和minute，因为它们可能是0
    if (userInfo.hour === undefined || userInfo.hour === null || 
        userInfo.minute === undefined || userInfo.minute === null) {
      missingFields.push('time');
    }
    
    if (missingFields.length > 0) {
      this.logger.error('用户信息不完整', { missingFields });
      wx.showToast({
        title: '信息不完整，请重新输入',
        icon: 'none'
      });
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/input-info/input-info'
        });
      }, 1500);
      return;
    }

    // 保存用户信息到页面数据中，以便后续刷新
    this.setData({ userInfo });
    
    this.startAnalysis(userInfo);
  },

  // 自定义返回按钮行为
  onBackPress: function() {
    this.logger.debug('用户点击了左上角返回按钮');
    
    // 正确处理返回动作，尝试返回到真正的上一页
    wx.navigateBack({
      delta: 1,
      fail: () => {
        // 如果无法返回上一页，则跳转到输入信息页
        wx.redirectTo({
          url: '/pages/input-info/input-info'
        });
      }
    });
    
    // 返回true表示自己处理返回逻辑，阻止默认行为
    return true;
  },

  // 添加页面卸载处理函数
  onUnload: function() {
    this.logger.debug('结果页面卸载');
    
    // 清理分析阶段更新定时器
    this._stopAnalysisStepUpdates();
    
    // 清理任何可能的定时器或异步操作
    if (this._analysisPending) {
      this.logger.debug('取消未完成的分析请求');
      this._analysisPending = false;
    }
  },

  // 返回上一页
  navigateBack: function() {
    this.logger.debug('用户点击了返回按钮');
    
    // 尝试返回到真正的上一页
    wx.navigateBack({
      delta: 1,
      fail: () => {
        // 如果无法返回上一页，则跳转到输入信息页
        this.logger.debug('无法返回上一页，跳转到输入页面');
        wx.redirectTo({
          url: '/pages/input-info/input-info'
        });
      }
    });
  },

  startAnalysis: async function(userInfo) {
    try {
      this.logger.info('开始分析');
      
      this.setData({ 
        isLoading: true, 
        error: null,
        isProcessing: false,
        analysisStep: '正在准备数据...'
      });

      // 启动分析阶段更新定时器
      this._startAnalysisStepUpdates();

      // 标记分析正在进行中
      this._analysisPending = true;
      
      const result = await this.callAnalysisAPI(userInfo);
      
      // 如果页面已卸载，不再继续处理
      if (!this._analysisPending) {
        this.logger.debug('页面已卸载，取消后续处理');
        return;
      }
      
      // 停止分析阶段更新
      this._stopAnalysisStepUpdates();
      
      this.logger.debug('API返回结果类型', { success: result?.success });
      
      if (result && result.success) {
        // 先清理文本，再转换为HTML
        const cleanedBasicAnalysis = this.cleanMarkdownText(result.basicAnalysis);
        const formattedBasicAnalysis = this.convertMarkdownToHtml(cleanedBasicAnalysis);
        
        this.setData({
          basicAnalysis: result.basicAnalysis,
          formattedResult: formattedBasicAnalysis,
          showResult: true,
          isLoading: false,
          isProcessing: false,
          analysisStep: '分析完成'
        });
        
        this.logger.info('分析完成，显示结果');
      } else {
        throw new Error(result?.error || '分析失败');
      }
      
      // 分析完成，清除标记
      this._analysisPending = false;
    } catch (error) {
      // 如果页面已卸载，不再继续处理
      if (!this._analysisPending) {
        this.logger.debug('页面已卸载，取消错误处理');
        return;
      }
      
      // 停止分析阶段更新
      this._stopAnalysisStepUpdates();
      
      // 专门检查错误码40001，表示"请求重复/正在处理中"
      if (error.code === 40001) {
        this.logger.info('检测到错误码40001：请求已在处理中');
        this.setData({
          isLoading: false,
          error: null,
          isProcessing: true  // 设置为处理中状态
        });
        return;
      }
      
      this.logger.error('分析过程出错', { message: error.message });
      this.setData({
        isLoading: false,
        isProcessing: false,
        error: error.message || '分析失败，请重试',
        analysisStep: '分析出错'
      });
      wx.showToast({
        title: error.message || '分析失败，请重试',
        icon: 'none',
        duration: 2000
      });
      
      // 错误处理完成，清除标记
      this._analysisPending = false;
    }
  },

  callAnalysisAPI: function(userInfo) {
    // 确保所有字段都是字符串类型
    const data = {
      year: String(userInfo.year),
      month: String(userInfo.month),
      day: String(userInfo.day),
      hour: String(userInfo.hour),
      minute: String(userInfo.minute),
      location: String(userInfo.location),
      gender: String(userInfo.gender),
      submissionId: userInfo.submissionId,
      clientTimestamp: Date.now(),
      infoHash: userInfo.infoHash
    };

    // 如果有经度信息，添加到请求数据中
    if (userInfo.longitude) {
      data.longitude = String(userInfo.longitude);
    } else {
      this.logger.warn('用户信息中没有经度数据，将使用后端地名匹配');
    }

    this.logger.info('发送分析请求');
    this.logger.debug('请求数据类型', {
      hasYear: !!data.year,
      hasMonth: !!data.month,
      hasDay: !!data.day,
      hasHour: !!data.hour,
      hasLocation: !!data.location,
      hasGender: !!data.gender
    });

    return new Promise((resolve, reject) => {
      
      const currentEnv = this.config.CURRENT_ENV || 'development';
      const apiBaseUrl = this.config.API_ENV[currentEnv];
      this.logger.debug('使用API服务器地址:', apiBaseUrl);
      
      // 存储请求对象，以便可以在需要时取消
      const requestTask = wx.request({
        url: `${apiBaseUrl}/api/v1/analyze`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: data,
        timeout: 300000,  // 增加超时时间到300秒，与后端保持一致
        success: (res) => {
          // 检查页面是否已卸载
          if (!this._analysisPending) {
            this.logger.debug('页面已卸载，忽略API响应');
            return;
          }
          
          this.logger.debug('API响应状态', { statusCode: res.statusCode });
          
          if (res.statusCode === 200) {
            // 检查是否有特定错误码表示请求重复
            if (res.data && res.data.code === 40001) {
              // 错误码40001表示请求重复/正在处理中
              this.logger.info('检测到请求重复，错误码: 40001');
              const error = new Error('请求已在处理中');
              error.code = 40001;
              reject(error);
              return;
            }
            
            if (res.data && res.data.code === 200) {

              if (res.data.data) {
                // 新格式：包含basicAnalysis
                if (res.data.data.basicAnalysis !== undefined) {
                  resolve({
                    success: true,
                    basicAnalysis: res.data.data.basicAnalysis || ''
                  });
                }
                else {
                  this.logger.error('API响应格式不符合预期');
                  reject(new Error('服务器返回的数据格式有误'));
                }
              } else {
                this.logger.error('API响应中缺少data字段');
                reject(new Error('服务器返回的数据格式有误'));
              }
            } else {
              this.logger.error('API响应错误码', { 
                code: res.data?.code, 
                message: res.data?.message 
              });
            }
          } else {
            this.logger.error('HTTP状态错误', { statusCode: res.statusCode });
            reject(new Error(`服务器返回错误状态码：${res.statusCode}`));
          }
        },
        fail: (error) => {
          // 检查页面是否已卸载
          if (!this._analysisPending) {
            this.logger.debug('页面已卸载，忽略API错误');
            return;
          }
          
          this.logger.error('请求失败', { errMsg: error.errMsg });
          // 判断是否是超时错误
          if (error.errMsg && error.errMsg.includes('timeout')) {
            reject(new Error('请求超时，分析可能需要较长时间，请重试'));
          } else {
            reject(new Error(error.errMsg || '网络请求失败'));
          }
        }
      });
      
      // 存储请求任务，以便在页面卸载时可以取消
      this._currentRequest = requestTask;
      
      // 在onUnload中添加取消请求的逻辑
      const originalOnUnload = this.onUnload;
      this.onUnload = function() {
        if (this._currentRequest) {
          this.logger.debug('取消未完成的网络请求');
          this._currentRequest.abort();
          this._currentRequest = null;
        }
        
        if (originalOnUnload) {
          originalOnUnload.call(this);
        }
      };
    });
  },

  // 添加刷新结果按钮的处理函数
  refreshResult: function() {
    if (!this.data.userInfo) {
      wx.showToast({
        title: '用户信息无效，请重新输入',
        icon: 'none'
      });
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/input-info/input-info'
        });
      }, 1500);
      return;
    }
    
    this.logger.info('用户点击刷新按钮，重新获取分析结果');
    wx.showToast({
      title: '正在刷新...',
      icon: 'loading',
      duration: 1000
    });
    
    // 重新开始分析
    this.startAnalysis(this.data.userInfo);
  },

  retryAnalysis: function() {
    this.logger.info('用户重新开始分析');
    wx.redirectTo({
      url: '/pages/input-info/input-info'
    });
  },

  // 添加分析阶段更新函数
  _startAnalysisStepUpdates: function() {
    if (this._analysisTimer) {
      clearInterval(this._analysisTimer);
    }
    
    // 记录分析开始时间
    this._analysisStartTime = Date.now();
    
    // 创建分析阶段更新定时器
    this._analysisTimer = setInterval(() => {
      const elapsedSeconds = Math.floor((Date.now() - this._analysisStartTime) / 1000);
      let stepText = '';
      
      if (elapsedSeconds < 8) {
        stepText = '准备数据中...';
      } else if (elapsedSeconds < 15) {
        stepText = '开始智能分析...';
      } else if (elapsedSeconds < 25) {
        stepText = '人工智能在拼命分析中...';
      } else if (elapsedSeconds < 35) {
        stepText = '正在生成详细分析结果...';
      } else if (elapsedSeconds < 45) {
        stepText = '只差最后一步了...';
      } else {
        // 计算分钟和秒数
        const minutes = Math.floor(elapsedSeconds / 60);
        const seconds = elapsedSeconds % 60;
        stepText = `分析进行中，已用时${minutes}分${seconds}秒`;
      }
      
      this.setData({ analysisStep: stepText });
    }, 3000); // 每3秒更新一次
  },
  
  // 停止分析阶段更新
  _stopAnalysisStepUpdates: function() {
    if (this._analysisTimer) {
      clearInterval(this._analysisTimer);
      this._analysisTimer = null;
    }
  },

  // 清理Markdown文本
  cleanMarkdownText: function(text) {
    if (!text) return '';

    // 保存代码块，防止处理过程中破坏代码格式
    const codeBlocks = [];
    text = text.replace(/```([\s\S]*?)```/g, function(match) {
      codeBlocks.push(match);
      return `__CODE_BLOCK_${codeBlocks.length - 1}__`;
    });

    // 分行处理
    let lines = text.split('\n');
    const processedLines = [];

    for (let i = 0; i < lines.length; i++) {
      // 移除行首尾的空白字符
      let line = lines[i].trim();
      
      if (line === '') {
        processedLines.push('');
        continue;
      }
      
      // 处理标题，确保"#"和文本之间只有一个空格
      if (line.startsWith('#')) {
        line = line.replace(/^(#+)\s*/, '$1 ');
      }
      
      // 处理【】格式的标题，确保其后有换行
      if (line.match(/【.*】/)) {
        // 如果该行只包含【】格式的标题，将其视为一级标题
        if (line.match(/^【.*】$/)) {
          line = '# ' + line;
        }
        // 否则，在【】后面添加强调
        else {
          line = line.replace(/【(.*?)】/g, '**【$1】**');
        }
      }
      
      // 处理列表项，确保"-"、"*"、"+"或数字后面只有一个空格
      if (line.match(/^[-*+]\s/) || line.match(/^\d+\.\s/)) {
        line = line.replace(/^([-*+])\s+/, '$1 ');
        line = line.replace(/^(\d+\.)\s+/, '$1 ');
      }
      
      // 处理引用，确保">"后只有一个空格
      if (line.startsWith('>')) {
        line = line.replace(/^>\s*/, '> ');
      }
      
      processedLines.push(line);
    }
    
    // 重新组合文本
    text = processedLines.join('\n');
    
    // 确保段落之间只有一个空行
    text = text.replace(/\n{3,}/g, '\n\n');
    
    // 恢复代码块
    text = text.replace(/__CODE_BLOCK_(\d+)__/g, function(match, index) {
      return codeBlocks[parseInt(index)];
    });
    
    return text;
  },

  // 转换markdown为HTML
  convertMarkdownToHtml: function(markdown) {
    if (!markdown) return '';

    let html = markdown;

    // 处理代码块
    html = html.replace(/```([\s\S]*?)```/g, function(match, code) {
      // 移除代码内的所有<br>标签
      code = code.replace(/<br\s*\/?>/g, '\n').trim();
      return '<pre><code>' + code + '</code></pre>';
    });

    // 特别处理【】格式的内容，确保正确换行
    // 1. 处理开头是【】的行，将其作为标题
    html = html.replace(/^(【[^】]+】)(.*)$/gm, function(match, title, rest) {
      if (rest.trim()) {
        // 如果标题后还有内容，将标题单独作为一行，内容另起一行
        return '<div class="bracket-title">' + title + '</div><p>' + rest + '</p>';
      } else {
        // 如果只有标题，直接返回标题
        return '<div class="bracket-title">' + title + '</div>';
      }
    });
    
    // 2. 处理行内的【】标记(不在行开头)
    html = html.replace(/([^<>])【([^】]+)】/g, function(match, prev, content) {
      // 强调显示行内的【】标记，添加特殊的类名
      return prev + '<strong class="bracket-inline">【' + content + '】</strong>';
    });

    // 特别处理中文数字标题（一、二、三...）
    html = html.replace(/^([一二三四五六七八九十]+)[、：:](?:\s*)(.*?)$/gm, function(match, num, content) {
      const trimmedContent = content.trim();
      if (trimmedContent) {
        return `<div class="chinese-title">${num}、${trimmedContent}</div>`;
      }
      return match;
    });

    // 处理标题
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
    html = html.replace(/^##### (.*$)/gm, '<h5>$1</h5>');
    html = html.replace(/^###### (.*$)/gm, '<h6>$1</h6>');

    // 处理加粗和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 将文本分行处理列表
    const lines = html.split('\n');
    const processedLines = [];
    
    let inList = false;
    let listType = '';
    
    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];
      
      // 处理无序列表
      if (line.match(/^[-*+] (.*)/)) {
        if (!inList || listType !== 'ul') {
          if (inList) {
            // 结束之前的列表
            processedLines.push(listType === 'ol' ? '</ol>' : '</ul>');
          }
          processedLines.push('<ul>');
          inList = true;
          listType = 'ul';
        }
        line = line.replace(/^[-*+] (.*)/, '<li>$1</li>');
        processedLines.push(line);
      }
      // 处理有序列表
      else if (line.match(/^\d+\. (.*)/)) {
        if (!inList || listType !== 'ol') {
          if (inList) {
            // 结束之前的列表
            processedLines.push(listType === 'ol' ? '</ol>' : '</ul>');
          }
          processedLines.push('<ol>');
          inList = true;
          listType = 'ol';
        }
        line = line.replace(/^\d+\. (.*)/, '<li>$1</li>');
        processedLines.push(line);
      }
      // 处理非列表项
      else {
        if (inList) {
          processedLines.push(listType === 'ol' ? '</ol>' : '</ul>');
          inList = false;
        }
        processedLines.push(line);
      }
    }
    
    // 确保所有列表都关闭
    if (inList) {
      processedLines.push(listType === 'ol' ? '</ol>' : '</ul>');
    }
    
    html = processedLines.join('\n');
    
    // 处理引用
    html = html.replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>');

    // 处理段落（将连续的行合并为一个段落）
    const paragraphs = html.split(/\n{2,}/);
    
    html = paragraphs.map(p => {
      // 跳过已经有HTML标签的内容
      if (p.trim() === '' || p.match(/^<(\/?)(h[1-6]|pre|blockquote|ul|ol|li|div).*>/) || p.match(/<(\/?)(h[1-6]|pre|blockquote|ul|ol|li|div).*>$/)) {
        return p;
      }
      return `<p>${p.replace(/\n/g, '<br>')}</p>`;
    }).join('\n\n');

    // 处理内联代码
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // 处理链接和图片
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1">');

    // 处理分隔线
    html = html.replace(/^---+$/gm, '<hr>');
    
    return html;
  }
}); 