/* input-info.wxss */

/* ==================== 页面主容器 ==================== */
.container {
    /* 容器尺寸 */
    padding: var(--spacing-200);
    min-height: 100vh; /* 确保容器至少占满整个屏幕高度 */
    /* 容器样式 */
    background:linear-gradient(0deg, #ffffff 0%,#EAD4FF 20%, #a175e7 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box; 
  }
  
  /* ==================== 1. 表单 ==================== */
  .form-container {
    /* 容器尺寸 */
    padding: var(--spacing-300);
    min-height: 85vh; /* 确保容器至少占满整个屏幕85%高度 */ 
    /* 容器样式 */
    background-color: #fff;
    border-radius: var(--radius-medium);
    border: 1rpx solid var(--color-bluepurple-light4);
    box-shadow: 0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
    margin-top: var(--spacing-200);
  }
  
  /*  表单头部区域 */
  .form-header {
    /* 容器布局 */
    text-align: center;
    margin-bottom: var(--spacing-200);
  }
  
  .form-title {
    /* 容器布局 */
    margin-bottom: var(--spacing-050);
    
    /* 字体样式 */
    font-size: var(--font-size-large);
    font-weight: bold;
    color: var(--color-bluepurple);
    letter-spacing: 0.1em; /* 增加字间距 */
  }
  
  .form-subtitle {
    /* 字体样式 */
    font-size: var(--font-size-normal);
    color: var(--color-bluegray-dark5);
    letter-spacing: 0.5rpx; /* 0.5rpx用于所有正常字号文本内容，增加字间距 */
  }
  
  /*  表单项  */
  .form-item {
    /* 容器布局 */
    margin-bottom: var(--spacing-050);
  }
  
  /* 表单标签容器 */
  .form-label {
    /* 容器布局 */
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-050);
  }
  
  /* 表单标签文字 */
  .form-label text {
    /* 字体样式 */
    font-size: var(--font-size-normal);
    color: var(--color-bluepurple);
    font-weight: 500;
  }
  
  /* 标签图标容器 */
  .label-icon {
    /* 容器布局 */
    margin-right: 8rpx;  
    /* 图标样式 */
    font-size: 30rpx;
  }
  
  /* 表单输入框容器 */
  .form-input {
    /* 容器布局 */
    height: 80rpx;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    
    /* 容器样式 */
    background-color: var(--color-bluepurple-light4);
    border: 2rpx solid transparent;
    border-radius: var(--radius-medium);
  }
  
  /* 表单输入框文字 */
  .form-input text {
    /* 字体样式 */
    font-size: var(--font-size-normal);
    color: var(--color-bluegray-dark5);
  }
  
  /* ==================== 2. 按钮区域 ==================== */
  .button-container {
    /* 容器布局 */
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    margin-top: var(--spacing-700);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-200);
  }
  
  /* 按钮基础容器 */
  .submit-btn {
    /* 容器布局 */
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60%;
    height: 80rpx;
    padding: 12rpx 24rpx;
    box-sizing: border-box;
    
    /* 容器样式 */
    border-radius: var(--radius-large);
    position: relative;
    overflow: hidden;
  }
  
  /* 按钮文字 */
  .submit-btn text {
    /* 字体样式 */
    font-size: var(--font-size-medium);
    font-weight: bold;
    color: var(--color-bluegray-dark5);
    white-space: nowrap;
  }
  
  /* 主要按钮样式 */
  .submit-btn.primary {
    /* 容器样式 */
    background: var(--color-bluegray-light1);
    box-shadow: 0rpx 4rpx 6rpx var(--color-bluegray), 0rpx 8rpx 16rpx var(--color-bluegray-light1); /*柔和优雅的双层阴影,普通按钮*/
  }
  
  /* 次要按钮样式 */
  .submit-btn.secondary {
    /* 容器样式 */
    background: var(--color-bluepurple-light2);
    box-shadow: 0rpx 4rpx 6rpx var(--color-bluegray), 0rpx 8rpx 16rpx var(--color-bluegray-light1); /*柔和优雅的双层阴影,普通按钮*/
  }
  
  /* 确保按钮间距 - 兼容性处理 */
  .submit-btn:not(:last-child) {
    margin-bottom: var(--spacing-100);
  }
  
  /* ==================== 3. 模型信息区域 ==================== */
  .model-info {
    /* 容器布局 */
    margin-top: var(--spacing-200);
    padding: var(--spacing-200);
    background-color: #fff;
  }
  
  /* 信息项容器 */
  .info-item {
    /* 容器布局 */
    display: flex;
    align-items: center; /* 改为居中对齐 */
    justify-content: center; /* 添加水平居中 */
    margin-bottom: var(--spacing-100); /* 说明文字间距 */
  }
  
  /* 模型描述文本 */
  .model-desc {
    /* 容器布局 */
    display: block;
    text-align: center;
    
    /* 字体样式 */
    font-size: var(--font-size-small);
    color: var(--color-bluepurple);
    line-height: 1.66;/* 所有正文段落间距 */
    letter-spacing: 0.5rpx; 
  }
  