<!-- input-info.wxml -->
<view class="container">
  <view class="form-container">
    <view class="form-header">
      <view class="form-title">个人信息输入</view>
      <view class="form-subtitle">请填写您的出生信息以完成注册</view>
    </view>
    
    <view class="form-item">
      <view class="form-label">
        <text class="label-icon">📅</text>
        <text>出生日期</text>
      </view>
      <picker mode="date" 
              value="{{date}}" 
              start="1900-01-01" 
              end="2024-12-31" 
              bindchange="bindDateChange">
        <view class="form-input">
          <text>{{year}}年{{month}}月{{day}}日</text>
          <view class="picker-arrow"></view>
        </view>
      </picker>
    </view>

    <view class="form-item">
      <view class="form-label">
        <text class="label-icon">🕒</text>
        <text>出生时间</text>
      </view>
      <picker mode="time" 
              value="{{time}}" 
              bindchange="bindTimeChange">
        <view class="form-input">
          <text>{{timeString}}</text>
          <view class="picker-arrow"></view>
        </view>
      </picker>
    </view>

    <view class="form-item">
      <view class="form-label">
        <text class="label-icon">📍</text>
        <text>出生地点</text>
      </view>
      <picker mode="region" 
              bindchange="bindRegionChange" 
              value="{{region}}" 
              custom-item="{{customItem}}">
        <view class="form-input">
          <text>{{region[0]}}{{region[1]}}{{region[2]}}</text>
          <view class="picker-arrow"></view>
        </view>
      </picker>
    </view>

    <view class="form-item">
      <view class="form-label">
        <text class="label-icon">☘️</text>
        <text>性别</text>
      </view>
      <picker bindchange="bindGenderChange" 
              value="{{genderArray.indexOf(gender)}}" 
              range="{{genderArray}}">
        <view class="form-input">
          <text>{{gender}}</text>
          <view class="picker-arrow"></view>
        </view>
      </picker>
    </view>

    <view class="button-container">
      <button class="submit-btn primary" bindtap="onSubmitButtonTap" disabled="{{isSubmitting}}">
        <text>{{isSubmitting ? '提交中...' : '提交信息'}}</text>
      </button>
    </view>
    
    <view class="tips">
      <text class="tip-text">提交后您将解锁各种分析功能和免费报告</text>
    </view>
  </view>
</view>
