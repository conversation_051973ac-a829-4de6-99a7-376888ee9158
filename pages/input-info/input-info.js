// input-info.js

// 定义一个全局logger获取函数
function getLogger() {
  return getApp().globalData.logger;
}

// 防止重复提交标志
let isSubmitting = false;

// 添加安全提交处理函数
function safeguardSubmit(submitFunction) {
  return async function(...args) {
    if (isSubmitting) {
      wx.showToast({
        title: '请勿重复提交',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    isSubmitting = true;

    try {
      return await submitFunction.apply(this, args);
    } catch (error) {
      getLogger().error('表单提交错误:', error);
      throw error;
    } finally {
      // 延迟释放锁，防止意外的快速重复点击
      setTimeout(() => {
        isSubmitting = false;
      }, 1000);
    }
  };
}

// 安全导航函数
let navigationLock = false;
function safeNavigateTo(url, options = {}) {
  const logger = getLogger();

  if (navigationLock) {
    return false;
  }

  navigationLock = true;

  // 使用navigateTo而不是redirectTo，确保页面栈正常
  wx.navigateTo({
    url: url,
    ...options,
    success: (res) => {
      if (options.success) options.success(res);
      // 延迟释放锁，防止短时间内的重复导航
      setTimeout(() => {
        navigationLock = false;
      }, 1000);
    },
    fail: (err) => {
      logger.error('导航失败:', err);
      // 如果错误信息中包含 'is not the current page'，则忽略该错误
      if (err && err.errMsg && err.errMsg.indexOf('is not the current page') > -1) {
        logger.warn('检测到页面路由错误，不影响后续操作');
      } else if (options.fail) {
        options.fail(err);
      }
      navigationLock = false;
    },
    complete: (res) => {
      if (options.complete) options.complete(res);
    }
  });

  return true;
}

Page({
    data: {
      year: '',
      month: '',
      day: '',
      hour: '',
      minute: '',
      location: '',
      gender: '男',
      genderArray: ['男', '女'],
      date: '',
      time: '',
      timeString: '',
      isSubmitting: false,

      // 添加地区选择器数据
      region: ['四川省', '成都市', ''],
      customItem: '全部'
    },

    onLoad: function() {
      // 保存logger引用到页面实例
      this.logger = getLogger();

      // 尝试从缓存中获取用户信息
      const cachedUserInfo = wx.getStorageSync('userInfo');

      if (cachedUserInfo) {

        // 设置页面数据
        this.setData({
          year: cachedUserInfo.year,
          month: cachedUserInfo.month,
          day: cachedUserInfo.day,
          hour: cachedUserInfo.hour,
          minute: cachedUserInfo.minute,
          location: cachedUserInfo.location,
          gender: cachedUserInfo.gender,
          date: `${cachedUserInfo.year}-${String(cachedUserInfo.month).padStart(2, '0')}-${String(cachedUserInfo.day).padStart(2, '0')}`,
          time: `${String(cachedUserInfo.hour).padStart(2, '0')}:${String(cachedUserInfo.minute).padStart(2, '0')}`,
          timeString: `${String(cachedUserInfo.hour).padStart(2, '0')}:${String(cachedUserInfo.minute).padStart(2, '0')}`,
          region: cachedUserInfo.region || [cachedUserInfo.province || '四川省', cachedUserInfo.city || '成都市', cachedUserInfo.district || ''],
        });

      } else {

        // 默认使用测试数据
        const testData = {
          year: 1995,
          month: 1,
          day: 28,
          hour: 19,
          minute: 30,
          location: '河北省沧州市',
          gender: '男',
          date: '1995-01-28',
          timeString: '19:30',
          region: ['河北省', '沧州市', '']
        };

        this.setData(testData);
      }

      // 使用安全提交包装
      this.safeSubmitUserInfo = safeguardSubmit(this.submitUserInfo);
    },

    // 格式化时间
    formatTime: function(hour, minute) {
      const formattedHour = hour.toString().padStart(2, '0');
      const formattedMinute = minute.toString().padStart(2, '0');
      return `${formattedHour}:${formattedMinute}`;
    },

    // 日期选择器变化
    bindDateChange: function(e) {
      const selectedDate = e.detail.value;
      const [year, month, day] = selectedDate.split('-');
      this.setData({
        date: selectedDate,
        year: Number(year),
        month: Number(month),
        day: Number(day)
      });
    },

    // 时间选择器变化
    bindTimeChange: function(e) {
      const timeValue = e.detail.value;
      const [hours, minutes] = timeValue.split(':');
      this.setData({
        time: timeValue,
        hour: Number(hours),
        minute: Number(minutes),
        timeString: timeValue
      });
    },

    // 性别选择器变化
    bindGenderChange: function(e) {
      this.setData({ gender: this.data.genderArray[e.detail.value] });
    },

    // 地区选择器变化
    bindRegionChange: function(e) {
      const region = e.detail.value;
      // 如果省份为 "台湾省"，将其改为 "台湾"
      if (region[0] === '台湾省') {
        region[0] = '台湾';
      }

      // 构建完整地址
      const location = region[0] + region[1] + (region[2] ? region[2] : '');

      this.setData({
        region: region,
        location: location
      });
    },

    // 创建用户信息对象
    createUserInfo: function() {
      // 验证表单数据
      if (!this.data.year || !this.data.month || !this.data.day) {
        wx.showToast({ title: '请选择出生日期', icon: 'none' });
        return null;
      }

      if (this.data.hour === undefined || this.data.hour === null ||
          this.data.minute === undefined || this.data.minute === null) {
        wx.showToast({ title: '请选择出生时间', icon: 'none' });
        return null;
      }

      if (!this.data.region[0] || !this.data.region[1]) {
        wx.showToast({ title: '请选择出生地点', icon: 'none' });
        return null;
      }

      // 使用选择器后，location 应该总是有值，但我们仍然进行检查
      if (!this.data.location) {
        this.setData({ location: this.data.region[0] + this.data.region[1] });
      }

      // 生成用于唯一标识用户信息的字符串
      const infoString = `${this.data.year}-${this.data.month}-${this.data.day}-${this.data.hour}-${this.data.minute}-${this.data.location}-${this.data.gender}`;

      // 基于用户信息生成唯一ID
      const infoHash = this._hashString(infoString);
      const submissionId = `wx_${infoHash}_${Date.now()}`;

      this.logger.info('生成基于用户信息的唯一ID:', submissionId);

      // 创建用户信息对象
      return {
        year: this.data.year,
        month: this.data.month,
        day: this.data.day,
        hour: this.data.hour,
        minute: parseInt(this.data.minute),
        location: this.data.location, // 用户输入的地址或选择的地区
        // region: this.data.region, // location 字段已包含地区信息
        province: this.data.region[0],
        city: this.data.region[1],
        district: this.data.region[2],
        gender: this.data.gender,
        submissionId: submissionId, // 添加基于用户信息生成的唯一ID
        infoString: infoString,     // 保存原始信息字符串，用于后续比对
        infoHash: infoHash,         // 保存信息哈希值，方便调试
        createTime: Date.now()      // 记录创建时间
      };
    },

    // 一个简单的字符串哈希函数
    _hashString: function(str) {
      let hash = 0;
      if (str.length === 0) return hash;

      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }

      // 转换为正数并限制长度
      return Math.abs(hash).toString().substring(0, 8);
    },

    // 提交用户信息到后台
    submitUserInfo: function() {
      // 获取用户信息
      const userInfo = this.createUserInfo();
      if (!userInfo) return;

      this.setData({ isSubmitting: true });

      try {
        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', userInfo);

        // 获取注册信息
        const registerInfo = wx.getStorageSync('registerInfo');

        // 合并用户信息和注册信息
        const submitData = {
          ...registerInfo,
          ...userInfo
        };

        // 调用API创建用户
        const apiBaseUrl = getApp().globalData.apiBaseUrl;
        this.logger.info('API Base URL:', apiBaseUrl);

        if (!apiBaseUrl) {
          this.logger.error('API Base URL is undefined!');
          wx.showToast({
            title: 'API配置错误',
            icon: 'none'
          });
          this.setData({ isSubmitting: false });
          return;
        }

        wx.request({
          url: `${apiBaseUrl}/api/v1/user/register`,
          method: 'POST',
          data: submitData,
          header: {
            'content-type': 'application/json'
          },
          success: (res) => {
            this.logger.debug('用户信息提交成功:', res.data);

            if (res.data.code === 200) {
              // 清除注册临时信息
              try {
                wx.removeStorageSync('registerInfo');
              } catch (error) {
                this.logger.warn('清除注册临时信息失败:', error);
              }

              wx.showToast({
                title: '信息提交成功',
                icon: 'success',
                duration: 1500
              });

              // 跳转到功能页
              setTimeout(() => {
                wx.redirectTo({
                  url: '/pages/features/features'
                });
              }, 1500);
            } else {
              wx.showToast({
                title: res.data.message || '提交失败',
                icon: 'none'
              });
            }
          },
          fail: (error) => {
            this.logger.error('用户信息提交失败:', error);
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          },
          complete: () => {
            this.setData({ isSubmitting: false });
          }
        });
      } catch (error) {
        this.logger.error('保存用户信息失败:', error);
        wx.showToast({
          title: '保存信息失败',
          icon: 'none'
        });
        this.setData({ isSubmitting: false });
      }
    },

    // 添加提交按钮点击处理
    onSubmitButtonTap: function() {
      this.safeSubmitUserInfo();
    }
})
