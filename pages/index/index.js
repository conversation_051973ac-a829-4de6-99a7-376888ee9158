// index.js
const userUtils = require('../../utils/user-utils.js');

Page({
  data: {
    bubbles: [
      { text: '个性化分析', delay: 0 },
      { text: '潜能发掘', delay: 2 },
      { text: '人生规划', delay: 4 }
    ]
  },

  onLoad() {
    // 每8秒重新触发一次气泡动画
    setInterval(() => {
      this.setData({
        bubbles: [
          { text: '个性化分析', delay: 0 },
          { text: '潜能发掘', delay: 2 },
          { text: '人生规划', delay: 4 }
        ]
      });
    }, 8000);
  },

  startTest() {
    // 检查用户是否已登录
    const userInfo = userUtils.checkUserLogin();
    
    if (userInfo) {
      // 已登录，直接跳转到功能页
      wx.navigateTo({
        url: '/pages/input-info/input-info'
      });
    } else {
      // 未登录，跳转到登录页
      wx.navigateTo({
        url: '/pages/login/login'
      });
    }
  }
})
