/* index.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(90deg, #004aad 0%, #cb6ce6 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  margin-top: -260rpx;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  width: 100%;
  margin-bottom: 40rpx;
}

.rotating-image-container {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: rotate 10s linear infinite;
}

.rotating-image {
  width: 1200rpx;
  height: 1200rpx;
  object-fit: contain;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 气泡样式 */
.bubbles-container {
  position: absolute;
  top: 40%;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.bubble {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  color: #ffffff;
  opacity: 0;
}

.bubble:nth-child(1) {
  left: 10%;
  animation: bubbleRise1 6s ease-in-out infinite;
}

.bubble:nth-child(2) {
  left: 50%;
  transform: translateX(-50%);
  animation: bubbleRise2 6s ease-in-out infinite;
}

.bubble:nth-child(3) {
  right: 10%;
  animation: bubbleRise3 6s ease-in-out infinite;
}

@keyframes bubbleRise1 {
  0% {
    opacity: 0;
    transform: translateY(400rpx) translateX(0);
  }
  20% {
    opacity: 1;
    transform: translateY(300rpx) translateX(0);
  }
  80% {
    opacity: 1;
    transform: translateY(0) translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-100rpx) translateX(0);
  }
}

@keyframes bubbleRise2 {
  0% {
    opacity: 0;
    transform: translateY(400rpx) translateX(-50%);
  }
  20% {
    opacity: 1;
    transform: translateY(300rpx) translateX(-50%);
  }
  80% {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }
  100% {
    opacity: 0;
    transform: translateY(-100rpx) translateX(-50%);
  }
}

@keyframes bubbleRise3 {
  0% {
    opacity: 0;
    transform: translateY(400rpx) translateX(0);
  }
  20% {
    opacity: 1;
    transform: translateY(300rpx) translateX(0);
  }
  80% {
    opacity: 1;
    transform: translateY(0) translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-100rpx) translateX(0);
  }
}

.logo {
  width: 800rpx;
  height: 800rpx;
}

.logo-text {
  font-size: 44rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  margin-top: 8rpx;
}

.title-container {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.title {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.subtitle-container {
  margin-top: 0;
  margin-bottom: 0;
  text-align: center;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.features {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
  width: 100%;
  max-width: 600rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 24rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
}

.feature-icon {
  font-size: 40rpx;
}

.feature-text {
  font-size: 32rpx;
  color: #ffffff;
}

.start-btn-container {
  width: 100%;
  max-width: 600rpx;
  margin-bottom: 0;
  position: relative;
  z-index: 10;
}

.start-btn {
  background: linear-gradient(135deg, #722ed1 0%, #1890ff 100%);
  padding: 24rpx 48rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 10; /* 确保按钮在最上层 */
}

.start-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

.start-btn-text {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
}

.start-btn-arrow {
  font-size: 36rpx;
  color: #ffffff;
  transition: transform 0.3s ease;
}

.start-btn:active .start-btn-arrow {
  transform: translateX(4rpx);
}