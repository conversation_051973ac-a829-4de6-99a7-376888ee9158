<!--pages/reasoner-stream/reasoner-stream.wxml-->
<view class="container">
    <!-- 错误状态 -->
    <view class="error-container" wx:if="{{error}}">
        <view class="error-box">
            <icon type="{{isDuplicateRequest ? 'info' : 'warn'}}" size="64" color="{{isDuplicateRequest ? '#a175e7' : '#e64340'}}"></icon>
            <text class="error-title" style="color: {{isDuplicateRequest ? '#a175e7' : '#e64340'}}">{{isDuplicateRequest ? '请求冲突' : '发生错误'}}</text>
            <text class="error-message">{{error}}</text>
            <button class="retry-button" bindtap="retryAnalysis">{{isDuplicateRequest ? '刷新页面' : '重新开始'}}</button>
        </view>
    </view>

    <!-- 结果显示（包括流式渲染状态） -->
    <view class="result-container" wx:else>

        <!-- 新的顺序设计：先显示推理过程，再显示结论 -->
        <view class="sequential-content">
            <!-- 推理过程部分 -->
            <view class="content-section reasoning-section">
                <view class="section-header">
                    <text class="section-title">思考分析过程</text>
                </view>
                <view class="section-body reasoning-content">
                    <rich-text nodes="{{reasoningResult}}" space="nbsp"></rich-text>
                </view>
            </view>

            <!-- 结论分割线 -->
            <view class="section-divider">
                <view class="divider-line"></view>
                <text class="divider-text">分析结论</text>
                <view class="divider-line"></view>
            </view>

            <!-- 结论内容部分 -->
            <view class="content-section conclusion-section">
                <view class="section-body result-content">
                    <rich-text nodes="{{result}}" space="nbsp"></rich-text>
                </view>

                <!-- 付费处理中 -->
                <view class="payment-processing" wx:if="{{isPaymentProcessing}}">
                    <view class="loading-animation small"></view>
                    <text>正在处理付费请求...</text>
                </view>
            </view>
        </view>

        <!-- 付费引导和按钮 -->
        <view class="payment-guide-container" wx:if="{{!isStreaming && result && showPaymentButton}}" style="height: 222rpx; display: flex; box-sizing: border-box">
            <view class="guide-text-payment">付费解锁更多大运流年关键信息</view>
            <button class="payment-button" bindtap="handlePaymentRequest" style="position: relative; left: -2rpx; top: 12rpx; width: 349rpx; height: 91rpx; display: flex; box-sizing: border-box">
                <text>🔒付费解锁</text>
            </button>
        </view>

        <!-- 返回按钮 -->
        <view class="action-buttons">
            <button class="back-button" bindtap="navigateBack" style="position: relative; left: -4rpx; top: -54rpx; width: 339rpx; height: 77rpx; display: flex; box-sizing: border-box">返回</button>
        </view>
    </view>
</view>