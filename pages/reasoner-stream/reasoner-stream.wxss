/* pages/reasoner-stream/reasoner-stream.wxss */

/* 页面主容器 */
.container {
  padding: var(--spacing-200);
  min-height: 100vh;
  background: linear-gradient(0deg, #ffffff 0%, #EAD4FF 20%, #a175e7 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  box-sizing: border-box;
  position: relative;
}

/* 错误状态 */
.error-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* 确保垂直居中 */
  margin-top: var(--spacing-200);
  min-height: 80vh; /* 添加最小高度确保有足够空间居中 */
}

.error-box {
  width:100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-300); /* 添加内边距 */
  box-sizing: border-box; /* 确保padding不会增加元素总宽度 */
  background-color: #fff;
  border-radius: var(--radius-medium);
  box-shadow:0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
  border: 1rpx solid var(--color-bluepurple-light4);
}

.error-title {
  font-size: var(--font-size-large);
  font-weight: bold;
  margin: var(--spacing-200) 0;
}

.error-message {
  font-size: var(--font-size-normal);
  color: var(--color-bluegray-dark5);
  text-align: center;
  margin-bottom: var(--spacing-300);
  line-height: 1.6;
}

.retry-button {
  background-color: var(--color-bluepurple-light2);
  border-radius: var(--radius-large);
  min-width: 60%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-top: var(--spacing-200);
  box-shadow: 0rpx 4rpx 6rpx var(--color-bluegray), 0rpx 8rpx 16rpx var(--color-bluegray-light1); /*柔和优雅的双层阴影,普通按钮*/                 
  font-size: var(--font-size-medium);
  font-weight: bold;
  color: var(--color-bluegray-dark5);
}

/* 结果容器 */
.result-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-300);
  margin-top: var(--spacing-200);
  margin-bottom: var(--spacing-300);
}

/* 删除标签导航样式 */
.tab-nav {
  display: none;
}

.tab {
  display: none;
}

.tab-content {
  display: none;
}

/* 新的顺序设计样式 */
.sequential-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-300);
}

.content-section {
  width: 100%;
  background-color: #fff;
  border-radius: var(--radius-medium);
  border: 1rpx solid var(--color-bluepurple-light4);
  box-shadow: 0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
  overflow: hidden;
  margin-bottom: var(--spacing-200);
}

.section-header {
  padding: var(--spacing-200);
  background-color: var(--color-bluepurple-light3);
  border-bottom: 1rpx solid var(--color-bluepurple-light4);
}

.section-title {
  font-size: var(--font-size-medium);
  font-weight: bold;
  color: var(--color-bluepurple-dark1);
}

.section-body {
  padding: var(--spacing-300);
}

/* 推理部分特殊样式 */
.reasoning-section {
  background-color: #f8f9fa;
}

.reasoning-section .section-body {
  background-color: var(--color-bluepurple-light4);
  padding: var(--spacing-400) var(--spacing-400);
  min-height: 400rpx;
  font-size: 24rpx; /* 调整为24rpx */
  line-height: 1.5; /* 调整更紧凑的行高 */
  color: var(--color-bluegray-dark3); /* 更淡的颜色 */
}

/* 思考分析过程中的rich-text内容样式 */
.reasoning-content rich-text {
  font-size: 24rpx !important; /* 调整为24rpx */
  line-height: 1.5;
  color: var(--color-bluegray-dark3);
  letter-spacing: 0.2rpx;
}

/* 思考分析过程中的标题和段落样式 */
.reasoning-content h1,
.reasoning-content h2,
.reasoning-content h3,
.reasoning-content .chinese-num-title {
  font-size: 28rpx !important; /* 调整为28rpx */
  margin-top: 16rpx !important;
  margin-bottom: 12rpx !important;
}

.reasoning-content p {
  margin-bottom: 14rpx !important;
  font-size: 24rpx !important; /* 调整为24rpx */
}

/* 结论部分特殊样式 */
.conclusion-section {
  background-color: #fff;
}

.conclusion-section .section-body {
  background-color: #fff;
  background-image: linear-gradient(to bottom, rgba(161, 117, 231, 0.02), rgba(161, 117, 231, 0.01));
  padding: var(--spacing-400) var(--spacing-400);
  min-height: 300rpx;
}

/* 分割线样式 */
.section-divider {
  display: flex;
  align-items: center;
  margin: var(--spacing-300) 0;
  padding: 0 var(--spacing-300);
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background-color: var(--color-bluepurple-light3);
}

.divider-text {
  margin: 0 var(--spacing-200);
  color: var(--color-bluepurple);
  font-size: var(--font-size-medium);
  font-weight: bold;
}

/* 结果卡片 */
.result-card {
  width: 100%;
  background-color: #fff;
  border-radius: 0 0 var(--radius-medium) var(--radius-medium);
  border: 1rpx solid var(--color-bluepurple-light4);
  box-shadow: 0 12rpx 24rpx var(--color-bluepurple-light2), 0 2rpx 6rpx var(--color-bluepurple-light3);
  overflow: hidden;
}

/* 结果内容 */
.result-content {
  padding: var(--spacing-400) var(--spacing-400);
  min-height: 400rpx;
  background-color: #fff;
  background-image: linear-gradient(to bottom, rgba(161, 117, 231, 0.02), rgba(161, 117, 231, 0.01));
  font-size: var(--font-size-normal);
  line-height: 1.8;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  word-break: break-word;
  white-space: pre-wrap;
}

/* 中文数字标题样式加强 - 参考simple-result */
.chinese-num-title {
  margin-top: 40rpx !important;
  margin-bottom: 30rpx !important;
  font-size: 34rpx !important;
  font-weight: bold !important;
  color: var(--color-bluepurple-dark1) !important;
  position: relative;
  padding-bottom: 16rpx;
  letter-spacing: 1px;
  border-bottom: 2rpx solid rgba(161, 117, 231, 0.2);
  clear: both;
}

/* 段落样式增强 - 参考simple-result */
.result-content p {
  margin-bottom: 24rpx !important;
  text-align: justify;
  text-justify: inter-character;
  line-height: 1.66;
  letter-spacing: 0.5rpx;
  word-break: break-word;
  color: var(--color-bluegray-dark5);
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}

/* 破折号处理优化 */
.dash-line-break {
  display: block;
  padding-top: 16rpx;
  padding-bottom: 12rpx;
  color: var(--color-bluepurple-dark1);
  font-weight: 500;
  position: relative;
  margin: 10rpx 0;
}

.dash-line-break::before {
  content: "";
  position: absolute;
  left: -20rpx;
  top: 50%;
  width: 10rpx;
  height: 10rpx;
  background-color: var(--color-bluepurple-light2);
  border-radius: 50%;
  transform: translateY(-50%);
}

/* 标题样式 */
.result-content h1, 
.result-content h2 {
  font-size: var(--font-size-large);
  font-weight: bold;
  margin: 30rpx 0 20rpx;
  color: var(--color-bluepurple);
  border-bottom: 3rpx solid var(--color-bluepurple-light3);
  clear: both;
}

.result-content h3 {
  font-size: var(--font-size-medium);
  font-weight: bold;
  margin: 25rpx 0 18rpx;
  color: var(--color-bluepurple-dark1);
  clear: both;
}

/* 内容分隔线 - 参考simple-result */
.content-separator {
  height: 2rpx;
  background: linear-gradient(to right, transparent, var(--color-bluepurple-light2), transparent);
  margin: 30rpx 0;
  position: relative;
}

.content-separator::before {
  content: "✦";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 0 var(--spacing-100);
  color: var(--color-bluepurple);
  font-size: var(--font-size-small);
}

/* 引导文本和按钮组容器 */
.guide-and-actions {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-300);
  margin-bottom: var(--spacing-200);
}

/* 内容区域中的引导文本 - 保留原有样式 */
.guide-text {
  padding: var(--spacing-200);
  text-align: center;
  font-size: var(--font-size-normal);
  color: var(--color-orange);
  font-weight: bold;
  background-color: var(--color-bluepurple-light4);
  border-radius: var(--radius-small);
  margin: var(--spacing-300) var(--spacing-200);
}

/* 底部引导文本 */
.guide-text-footer {
  text-align: center;
  font-size: var(--font-size-normal);
  color: var(--color-orange);
  font-weight: bold;
  background-color: rgba(255, 165, 0, 0.1);
  border-radius: var(--radius-medium);
  padding: var(--spacing-200);
  margin-bottom: var(--spacing-200);
  width: 90%;
  box-shadow: 0 2rpx 8rpx rgba(255, 165, 0, 0.2);
}

/* 更多按钮容器 */
.more-button-container {
  padding: var(--spacing-300) var(--spacing-400);
  display: flex;
  justify-content: center;
}

/* 付费解锁按钮 */
.premium-unlock-btn {
  background: linear-gradient(135deg, var(--color-gold), var(--color-gold-light));
  color: #fff;
  font-size: var(--font-size-medium);
  font-weight: bold;
  padding: var(--spacing-100) var(--spacing-250);
  border-radius: var(--radius-large);
  box-shadow: 0 8rpx 16rpx rgba(255, 193, 24, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  height: 88rpx;
  line-height: 1.2;
}

/* 操作按钮区域 */
.action-buttons {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-300);
}

/* 返回按钮 */
.back-button {
  background: linear-gradient(135deg, #8c63e3 0%, #6236bc 100%);
  color: white;
  font-weight: bold;
  font-size: var(--font-size-medium);
  border-radius: var(--radius-large);
  width: 85%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(98, 54, 188, 0.3);
  transition: all 0.3s ease;
}

.back-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(98, 54, 188, 0.2);
}

/* 流式渲染提示 */
.streaming-indicator {
  display: flex;
  align-items: center;
  background-color: rgba(161, 117, 231, 0.1);
  padding: var(--spacing-100) var(--spacing-200);
  border-radius: var(--radius-medium);
  margin-bottom: var(--spacing-200);
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.loading-animation.small {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--color-bluepurple-light3);
  border-top: 4rpx solid var(--color-bluepurple);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
  margin-right: var(--spacing-200);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.streaming-text {
  font-size: var(--font-size-small);
  color: var(--color-bluepurple);
}

/* 付费按钮样式 */
.payment-button-container {
  margin: 30rpx auto;
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.payment-button {
  background: linear-gradient(135deg, #FFD700, #FFC107);
  color: #5D4037;
  font-weight: bold;
  font-size: var(--font-size-medium);
  border-radius: var(--radius-large);
  width: 85%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(255, 215, 0, 0.35);
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 215, 0, 0.6);
}

.payment-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(255, 215, 0, 0.25);
}

.payment-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.payment-price {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  font-weight: normal;
  opacity: 0.9;
}

.payment-description {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-top: 8rpx;
}

/* 支付处理中样式 */
.payment-processing {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
  color: #6247aa;
  font-size: 28rpx;
}

.payment-processing .loading-animation {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
  border-color: #6247aa transparent #6247aa transparent;
}

/* 付费引导和按钮容器 */
.payment-guide-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 30rpx 0 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 付费引导文本 */
.guide-text-payment {
  font-size: var(--font-size-normal);
  font-weight: bold;
  color: #8B6914;
  text-align: center;
  margin-bottom: 20rpx;
  padding: 16rpx 24rpx;
  background-color: rgba(255, 215, 0, 0.1);
  border-radius: var(--radius-medium);
  width: 90%;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.15);
}