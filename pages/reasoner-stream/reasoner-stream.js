// 引入流式渲染行为
const streamRenderBehavior = require('../../behaviors/stream-render-behavior');

Page({
  // 引入流式渲染行为
  behaviors: [streamRenderBehavior],

  data: {
    isLoading: true,  // 保留加载状态供内部逻辑使用，但不再显示加载容器
    error: null,
    activeTab: 'result', // 默认展示分析结果标签页
    guideText: '',       // 二阶段分析引导文本
    isDuplicateRequest: false, // 标记是否为重复请求错误
    useWebSocketMode: true,    // 使用 WebSocket 模式
    showPaymentButton: false,  // 是否显示付费按钮
    isPaymentProcessing: false, // 是否正在处理付费
    infoHash: '', // 新增infoHash字段
    // pageActive: true, // pageActive 状态由 onLoad 和 onShow/onHide 控制
  },

  /**
   * 生命周期函数 — 页面加载
   */
  onLoad() {
    this.pageActive = true;
    this.logger = getApp().globalData.logger || console;
    this.config = require('../../config');

    this.logger.debug('reasoner-stream onLoad: behavior should handle socket init and acquire.');

    wx.setNavigationBarTitle({
      title: '推理模型分析'
    });

    setTimeout(() => {
      if (this.pageActive) {
        this.loadUserDataAndStartAnalysis();
      }
    }, 200);
  },

  /**
   * 生命周期函数 — 页面显示
   */
  onShow() {
    this.pageActive = true;
    this.logger.debug('reasoner-stream onShow triggered.');

    const socketClient = getApp().globalData.socketClient;
    if (socketClient && typeof socketClient.acquire === 'function') {
      socketClient.acquire();
      this.logger.debug('reasoner-stream onShow: socketClient acquired.');
    } else {
      this.logger.warn('reasoner-stream onShow: socketClient not found or acquire method missing.');
    }

    const userInfo = this.data.userInfo || wx.getStorageSync('userInfo') || {};
    if (userInfo.firstStageCompleted) {
      this.setData({
        showPaymentButton: true,
        isPaymentProcessing: false,
        isStreaming: false
      });
      this.logger.debug('reasoner-stream onShow: Restored payment button based on firstStageCompleted.');
    }

    if (typeof this._setupSocketEvents === 'function') {
      this._setupSocketEvents();
      this.logger.debug('reasoner-stream onShow: WebSocket events re-established via _setupSocketEvents.');
    } else {
      this.logger.warn('reasoner-stream onShow: _setupSocketEvents method not found in behavior.');
    }
  },

  /**
   * 生命周期函数 — 页面隐藏
   */
  onHide() {
    this.pageActive = false;
    this.logger.debug('reasoner-stream onHide triggered. Cleaning up resources.');
    if (typeof this.cleanupResources === 'function') {
      this.cleanupResources();
      this.logger.debug('reasoner-stream onHide: cleanupResources called.');
    } else {
      this.logger.warn('reasoner-stream onHide: cleanupResources method not found in behavior.');
    }
  },

  /**
   * 读取用户信息并启动第一阶段分析
   */
  loadUserDataAndStartAnalysis() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo) {
        this.handleError('未找到用户信息');
        return;
      }

      // 首先检查 infoHash，如果缺失，这是个关键错误
      if (!userInfo.infoHash) {
        this.handleError('用户信息中缺少必要的哈希值 (infoHash)');
        return;
      }

      // 然后检查其他必需字段
      const otherRequiredFields = ['year', 'month', 'day', 'gender'];
      const missingFields = otherRequiredFields.filter(field => userInfo[field] === undefined || userInfo[field] === null || userInfo[field] === '');
      
      // hour 可能为 0，所以要特别检查 undefined/null
      if (userInfo.hour === undefined || userInfo.hour === null) {
        missingFields.push('hour');
      }

      if (missingFields.length > 0) {
        const errorMessage = `用户信息不完整: 缺少 ${missingFields.join(', ')}。请返回完善信息。`;
        this.logger.error(errorMessage); // 记录更严重的错误
        this.handleError(errorMessage); // 直接报错并引导用户返回
        return;
      }

      this.setData({
        userInfo,
        infoHash: userInfo.infoHash
      });

      if (userInfo.firstStageCompleted && !this.data.isStreaming) {
        this.setData({
          showPaymentButton: true,
          isStreaming: false
        });
        this.logger.info('loadUserDataAndStartAnalysis: Detected completed first stage, showing payment button.');
      }

      if (typeof this.startAnalysis === 'function') {
        this.startAnalysis(userInfo);
      } else {
        this.logger.error('loadUserDataAndStartAnalysis: startAnalysis method not found in behavior.');
        this.handleError('分析功能未正确初始化');
      }
    } catch (err) {
      this.handleError(`读取用户数据出错: ${err.message}`);
    }
  },

  /**
   * 统一错误处理
   */
  handleError(message) {
    this.logger.error('reasoner-stream handleError:', message);
    this.setData({
      isLoading: false,
      isStreaming: false,
      error: message,
    });

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2500 // 延长显示时间以便用户阅读
    });

    setTimeout(() => {
      if (this.pageActive) {
        // 尝试返回到上一页，如果上一页是 input-info 则正好，否则可能需要更复杂的导航逻辑
        // 通常用户信息不全应该返回到信息输入页
        const pages = getCurrentPages();
        if (pages.length > 1 && pages[pages.length - 2].route.includes('input-info')) {
            wx.navigateBack({ delta: 1 });
        } else {
            wx.redirectTo({ url: '/pages/input-info/input-info' }); // 强制跳转到输入页
        }
      }
    }, 2500);
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  /**
   * 重新触发第一阶段分析
   */
  retryAnalysis() {
    this.logger.info('Retrying analysis...');
    if (typeof this._resetState === 'function') {
        this._resetState();
    } else {
        this.setData({
            isLoading: true,
            error: null,
            result: '',
            reasoningContent: '',
            isDuplicateRequest: false,
            showPaymentButton: false,
            guideText: ''
        });
    }
    this.loadUserDataAndStartAnalysis();
  },

  appendSecondStageGuide() {
    if (!this.data.result && !this.data.reasoningResult) return;
    this.logger.debug('Appending second stage guide text.');
    this.setData({
      guideText: '✨ 付费解锁获取更详细的大运流年关键建议 ✨',
    });
  },

  handlePaymentRequest() {
    this.logger.info('Payment request initiated.');
    this.setData({ isPaymentProcessing: true });

    wx.showLoading({ title: '处理付费请求...', mask: true });
    setTimeout(() => {
      wx.hideLoading();
      if (!this.pageActive) {
          this.logger.warn('Payment successful but page is not active. Aborting navigation.');
          this.setData({ isPaymentProcessing: false });
          return;
      }
      wx.showToast({ title: '支付成功，开始深度分析', icon: 'success', duration: 2000 });
      this.navigateToSecondStageStream();
    }, 1500);
  },

  navigateToSecondStageStream() {
    if (!this.data.infoHash) {
      wx.showToast({ title: '缺少必要的infoHash参数', icon: 'none', duration: 2000 });
      this.setData({ isPaymentProcessing: false });
      return;
    }

    this.setData({ isPaymentProcessing: false });
    this.logger.info(`Navigating to secondStage-stream with infoHash: ${this.data.infoHash}`);

    wx.navigateTo({
      url: `/pages/secondStage-stream/secondStage-stream?infoHash=${this.data.infoHash}`,
      fail: err => {
        this.logger.error('跳转到二阶段流式页面失败:', err);
        wx.showToast({ title: '页面跳转失败', icon: 'none', duration: 2000 });
      }
    });
  },

  async startSecondStageAnalysis() {
    this.logger.info('Starting second stage analysis directly on reasoner-stream page.');
    try {
      if (typeof this._resetState === 'function') {
        this._resetState();
      }
      this.setData({
        isPaymentProcessing: false,
        showPaymentButton: false,
        guideText: '',
        activeTab: 'result',
      });

      if (typeof this.requestSecondStage === 'function') {
        const success = await this.requestSecondStage();
        if (!success) {
          throw new Error('请求第二阶段分析失败 (from behavior)');
        }
        this.logger.info('Second stage analysis request successful.');
      } else {
        this.logger.error('startSecondStageAnalysis: requestSecondStage method not found in behavior.');
        throw new Error('深度分析功能未正确初始化');
      }
    } catch (error) {
      this.logger.error('启动第二阶段分析失败:', error);
      wx.showToast({ title: error.message || '启动深度分析失败，请重试', icon: 'none', duration: 2000 });
      this.setData({
        isPaymentProcessing: false,
        showPaymentButton: true,
        isLoading: false,
        isStreaming: false,
      });
    }
  },

  navigateBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => wx.redirectTo({ url: '/pages/input-info/input-info' })
    });
  },

  /**
   * 生命周期函数 — 页面卸载
   * 注意：behavior 的 detached() 会自动调用 cleanupResources。
   * 因此，页面自己的 onUnload 不需要再次手动调用 cleanupResources，以避免双重释放。
   */
  onUnload() {
    this.pageActive = false;
    this.logger.debug('reasoner-stream onUnload: Page unloading. Behavior detached will handle final cleanup.');
    // ⚠️ 原手动调用 this.cleanupResources() 已移除，交由 behavior.detached() 处理
    // try {
    //   if (typeof this.cleanupResources === 'function') {
    //     // this.cleanupResources(); // 移除此调用
    //   }
    // } catch (error) {
    //   this.logger.error('Error during page unload cleanup:', error);
    // }
  }
});