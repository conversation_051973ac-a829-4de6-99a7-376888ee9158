<!--pages/login/login.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">欢迎使用</text>
    <text class="subtitle">请选择登录方式</text>
  </view>

  <!-- 登录方式选择 -->
  <view class="login-tabs">
    <view class="tab {{currentTab === 'login' ? 'active' : ''}}" bindtap="switchTab" data-tab="login">
      登录
    </view>
    <view class="tab {{currentTab === 'register' ? 'active' : ''}}" bindtap="switchTab" data-tab="register">
      注册
    </view>
  </view>

  <!-- 登录表单 -->
  <view class="form-container" wx:if="{{currentTab === 'login'}}">
    <!-- 登录类型选择 -->
    <view class="login-type-tabs">
      <view class="type-tab {{loginType === 'email' ? 'active' : ''}}" bindtap="switchLoginType" data-type="email">
        邮箱
      </view>
      <view class="type-tab {{loginType === 'phone' ? 'active' : ''}}" bindtap="switchLoginType" data-type="phone">
        手机
      </view>
    </view>

    <!-- 邮箱登录 -->
    <view wx:if="{{loginType === 'email'}}">
      <view class="input-group">
        <input class="input" type="text" placeholder="请输入邮箱地址" value="{{loginForm.email}}" bindinput="onEmailInput" />
      </view>
      <view class="input-group">
        <input class="input" type="password" placeholder="请输入密码" value="{{loginForm.password}}" bindinput="onPasswordInput" />
      </view>
    </view>

    <!-- 手机号登录 -->
    <view wx:if="{{loginType === 'phone'}}">
      <view class="input-group">
        <input class="input" type="number" placeholder="请输入手机号" value="{{loginForm.phone}}" bindinput="onPhoneInput" />
      </view>
      <view class="input-group">
        <input class="input" type="password" placeholder="请输入密码" value="{{loginForm.password}}" bindinput="onPasswordInput" />
      </view>
    </view>

    <!-- 登录按钮 -->
    <button class="btn-primary" bindtap="handleLogin" disabled="{{isLoading}}">
      {{isLoading ? '登录中...' : '登录'}}
    </button>

    <!-- 微信授权登录 -->
    <view class="divider">
      <text>或</text>
    </view>
    <button class="btn-wechat" open-type="getUserInfo" bindgetuserinfo="handleWechatLogin">
      <image class="wechat-icon" src="/images/wechat-icon.png"></image>
      微信授权登录
    </button>
  </view>

  <!-- 注册表单 -->
  <view class="form-container" wx:if="{{currentTab === 'register'}}">
    <!-- 注册类型选择 -->
    <view class="login-type-tabs">
      <view class="type-tab {{registerType === 'email' ? 'active' : ''}}" bindtap="switchRegisterType" data-type="email">
        邮箱
      </view>
      <view class="type-tab {{registerType === 'phone' ? 'active' : ''}}" bindtap="switchRegisterType" data-type="phone">
        手机
      </view>
    </view>

    <!-- 邮箱注册 -->
    <view wx:if="{{registerType === 'email'}}">
      <view class="input-group">
        <input class="input" type="text" placeholder="请输入邮箱地址" value="{{registerForm.email}}" bindinput="onRegisterEmailInput" />
      </view>
      <view class="input-group">
        <input class="input" type="password" placeholder="请设置密码（至少6位）" value="{{registerForm.password}}" bindinput="onRegisterPasswordInput" />
      </view>
    </view>

    <!-- 手机号注册 -->
    <view wx:if="{{registerType === 'phone'}}">
      <view class="input-group">
        <input class="input" type="number" placeholder="请输入手机号" value="{{registerForm.phone}}" bindinput="onRegisterPhoneInput" />
      </view>
      <view class="input-group">
        <input class="input" type="password" placeholder="请设置密码（至少6位）" value="{{registerForm.password}}" bindinput="onRegisterPasswordInput" />
      </view>
    </view>

    <!-- 注册按钮 -->
    <button class="btn-primary" bindtap="handleRegister" disabled="{{isLoading}}">
      {{isLoading ? '注册中...' : '下一步：填写个人信息'}}
    </button>

    <!-- 微信授权注册 -->
    <view class="divider">
      <text>或</text>
    </view>
    <button class="btn-wechat" open-type="getUserInfo" bindgetuserinfo="handleWechatRegister">
      <image class="wechat-icon" src="/images/wechat-icon.png"></image>
      微信授权注册
    </button>
  </view>

  <!-- 提示信息 -->
  <view class="tips" wx:if="{{currentTab === 'register'}}">
    <text class="tip-text">注册后需要填写个人信息才能使用完整功能</text>
  </view>
</view>
