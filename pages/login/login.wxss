/* pages/login/login.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  margin-top: 80rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50rpx;
  margin-bottom: 40rpx;
  padding: 8rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 42rpx;
  color: rgba(255, 255, 255, 0.7);
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.tab.active {
  background: white;
  color: #667eea;
  font-weight: bold;
}

.form-container {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.login-type-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  padding: 4rpx;
}

.type-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 8rpx;
  color: #666;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.type-tab.active {
  background: #667eea;
  color: white;
}

.input-group {
  margin-bottom: 30rpx;
}

.input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.input:focus {
  border-color: #667eea;
}

.btn-primary {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary[disabled] {
  opacity: 0.6;
}

.divider {
  text-align: center;
  margin: 30rpx 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2rpx;
  background: #e0e0e0;
}

.divider text {
  background: white;
  padding: 0 20rpx;
  color: #999;
  font-size: 24rpx;
  position: relative;
  z-index: 1;
}

.btn-wechat {
  width: 100%;
  height: 88rpx;
  background: #07c160;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.tips {
  text-align: center;
  margin-top: 20rpx;
}

.tip-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 30rpx;
  }
  
  .title {
    font-size: 42rpx;
  }
  
  .form-container {
    padding: 30rpx;
  }
}
