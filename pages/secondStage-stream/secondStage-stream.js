// 引入流式渲染行为
const streamRenderBehavior = require('../../behaviors/stream-render-behavior');

Page({
  // 引入流式渲染行为
  behaviors: [streamRenderBehavior],
  
  data: {
    isLoading: false,  // 默认不显示加载状态
    error: null,
    analysisStep: '',  // 不显示分析步骤
    activeTab: 'result', // 默认展示分析结果标签页
    isDuplicateRequest: false, // 标记是否为重复请求错误
    useWebSocketMode: true,    // 使用 WebSocket 模式
    isPaymentProcessing: false, // 是否正在处理付费
    infoHash: '', // 存储信息哈希值
    isStreaming: false // 默认不处于流式渲染状态
  },

  /**
   * 自定义响应处理函数，处理后台返回的数据
   * 该方法会覆盖behavior中的同名方法
   */
  _handleResponse(data) {
    if (!data) return;
    
    // 判断响应是否成功
    const success = data.success !== undefined ? data.success : (data.code === 200 || data.code === 0);
    
    if (success) {
      // 如果有数据内容，提取并显示
      let result = '';
      let reasoningResult = '';
      
      // 仅从secondStage字段中提取内容
      if (data.data && data.data.secondStage) {
        const sst = data.data.secondStage;
        if (typeof sst === 'object') {
          result = sst.content || '';
          reasoningResult = sst.reasoningContent || '';
        } else if (typeof sst === 'string') {
          result = sst;
        }
      }
      
      // 如果有内容，更新UI
      if (result) {
        const fmt = typeof this.formatContent === 'function' ? this.formatContent(result) : result;
        const html = typeof this.convertMarkdownToHtml === 'function' ? this.convertMarkdownToHtml(fmt) : fmt;
        
        this.setData({
          result: html,
          content: result,
          isLoading: false,
          isStreaming: false,
          analysisStep: ''
        });
        
        this.logger.info('已加载并显示返回结果');
      }
      
      if (reasoningResult) {
        const fmtR = typeof this.formatContent === 'function' ? this.formatContent(reasoningResult) : reasoningResult;
        const htmlR = typeof this.convertMarkdownToHtml === 'function' ? this.convertMarkdownToHtml(fmtR) : fmtR;
        
        this.setData({
          reasoningResult: htmlR,
          reasoningContent: reasoningResult
        });
      }
      
      // 如果已经显示了内容就不再转发给behavior
      if (result || reasoningResult) {
        return;
      }
    } else if (data.code === 40001 || data.code === 202) {
      // 处理重复请求错误
      this.setData({
        isLoading: false,
        isStreaming: false,
        error: '分析请求正在处理中，请稍候刷新查看结果',
        analysisStep: '请求已存在',
        isDuplicateRequest: true
      });
      return;
    }
  },

  /**
   * 处理websocket onMessage事件
   * 直接处理raw message，不依赖行为转发
   */
  _handleRawMessage(msg) {
    if (!msg || !msg.data) return;
    
    try {
      // 尝试解析消息数据
      let data = msg.data;
      if (typeof data === 'string') {
        data = JSON.parse(data);
      }
      
      this.logger.debug('收到WebSocket消息:', data);
      
      // 简化判断，只要有数据就处理
      if (data.data) {
        // 调用响应处理方法
        this._handleResponse(data);
      }
    } catch (error) {
      this.logger.error('处理WebSocket消息出错:', error);
    }
  },

  /**
   * 生命周期函数 — 页面加载
   * 说明：
   *   1. WebSocket 单例由 app.js 在小程序启动时创建并连接。
   *   2. 这里负责完成事件绑定与心跳启动；通过 socketInitialized 标记防止重复调用。
   */
  onLoad(options) {
    this.pageActive = true;

    // 获取全局日志工具
    this.logger = getApp().globalData.logger;

    // 导入配置
    this.config = require('../../config');

    // ① 仅初始化一次事件监听与心跳
    if (!this.socketInitialized) {
      if (typeof this.initSocketClient === 'function') {
        this.initSocketClient();
      }
      this.socketInitialized = true;
    }

    // 添加直接的WebSocket消息监听，不依赖behavior转发
    const socketClient = getApp().globalData.socketClient;
    if (socketClient) {
      // 保存对原始消息的引用，用于卸载时解绑
      this._rawMessageHandler = this._handleRawMessage.bind(this);
      // 使用socketClient的on方法注册消息处理
      socketClient.on('message', this._rawMessageHandler);
      this.logger.debug('已添加原始WebSocket消息监听');
    }

    // 获取infoHash参数 (开发模式下提供默认值)
    let infoHash = options && options.infoHash;
    
    if (infoHash) {
      this.setData({ infoHash: infoHash });
      
      // 延迟启动分析，确保全局 socketClient 已就绪
      setTimeout(() => {
        if (this.pageActive) {
          this.startSecondStageAnalysis();
        }
      }, 0);
    } else {
      this.handleError('缺少必要的infoHash参数');
    }
  },
  
  /**
   * 统一错误处理
   */
  handleError(message) {
    this.logger.error(message);
    this.setData({
      isLoading: false,
      error: message,
      analysisStep: '分析失败'
    });

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 重新触发第二阶段分析
   */
  retryAnalysis() {
    if (!this.data.infoHash) {
      this.handleError('缺少必要的infoHash参数');
      return;
    }
    
    this.setData({
      error: null,
      analysisStep: '',
      result: '',
      reasoningContent: '',
      isDuplicateRequest: false
    });
    
    this.startSecondStageAnalysis();
  },

  /**
   * 第二阶段分析入口
   */
  async startSecondStageAnalysis() {
    try {
      // 不再设置isLoading为true，只清空内容
      this.setData({
        content: '',
        reasoningContent: '',
        result: '',
        reasoningResult: ''
      });

      const success = await this.requestSecondStage();
      if (!success) throw new Error('请求第二阶段分析失败');

      this.setData({
        analysisStep: '',  // 不显示"正在进行深度分析..."
        isStreaming: true  // 只设置streaming状态
      });
    } catch (error) {
      this.logger.error('启动第二阶段分析失败:', error);
      wx.showToast({ title: '启动深度分析失败，请重试', icon: 'none', duration: 2000 });
      this.setData({
        isLoading: false,
        error: error.message || '启动深度分析失败'
      });
    }
  },

  /**
   * 返回第一阶段分析页面
   */
  navigateToFirstStage() {
    // 直接返回上一页即可
    wx.navigateBack();
  },

  /**
   * 生命周期函数 — 页面卸载
   */
  onUnload() {
    this.logger.debug('页面卸载，清理资源');
    this.pageActive = false;
    
    // 移除WebSocket原始消息监听
    if (this._rawMessageHandler) {
      const socketClient = getApp().globalData.socketClient;
      if (socketClient) {
        try {
          // 使用socketClient的off方法解除消息处理
          socketClient.off('message', this._rawMessageHandler);
          this.logger.debug('WebSocket原始消息监听已移除');
        } catch (error) {
          this.logger.error('移除WebSocket消息监听失败:', error);
        }
      }
      this._rawMessageHandler = null;
    }
    
    try {
      if (typeof this.cleanupResources === 'function') {
        this.cleanupResources();
      }
    } catch (error) {
      console.error('页面卸载时出错:', error);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.error) {
      this.retryAnalysis();
    }
    wx.stopPullDownRefresh();
  },

}); 