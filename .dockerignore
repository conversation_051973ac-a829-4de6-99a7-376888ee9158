# 微信云托管 GitHub 流水线部署专用 .dockerignore
# 根目录部署模式：只保留 server 目录中的必要文件

# 排除所有文件和目录
*
*/

# 但保留 server 目录及其内容
!server/
!server/**

# 在 server 目录中排除不必要的文件
server/node_modules/
server/.git/
server/logs/
server/temp/*
!server/temp/.gitkeep

# 前端及项目文件 (已通过 * 排除，这里只是为了清晰)
pages/
components/
utils/
behaviors/
app.js
app.json
app.wxss
config.js
project.config.json
project.private.config.json

# npm 相关
server/node_modules
server/npm-debug.log*
server/yarn-debug.log*
server/yarn-error.log*
server/.npm
server/.yarn
server/.yarn-integrity

# 环境和机密文件 (即使在 server 目录中也要排除)
server/.env*
server/.env
server/.env.*
!server/.env.example

# CI/CD 配置 (但应保留部署需要的文件)
!.github/workflows/
!.github/actions/

# 缓存文件
server/**/.cache/
server/**/__pycache__/
server/.eslintcache
server/.parcel-cache

# 系统文件
server/**/.DS_Store
server/**/Thumbs.db
server/**/*.swp
server/**/*.swo

# 日志文件
server/logs
server/**/*.log

# 测试文件
server/__tests__
server/test
server/tests
server/**/*.spec.js
server/**/*.test.js
server/coverage

# 版本控制
server/.git
server/.gitignore

# 工具配置 (保留对构建有用的配置)
!server/package.json
!server/package-lock.json
!server/Dockerfile
!server/container.config.json
!server/requirements.txt
server/.vscode/
server/.idea/

# Python相关
server/**/__pycache__/
server/**/*.pyc
server/**/*.pyo
server/**/*.pyd
server/venv/
server/env/

# 文档 (除了必要的README和LICENSE)
server/**/*.md
!server/README.md
!server/LICENSE

# 备份和临时文件
server/**/*.bak
server/**/*.tmp
server/**/*~

# 保留微信云托管所需文件
!Dockerfile
!container.config.json
!server/wx_deploy.json
!server/wx_cloud_config.json



