# 八字分析项目架构与开发进度（v1.3）

## 项目概述

本项目是一个通过DeepSeek LLM提供分析服务的八字分析系统。**版本1.3已完成流式测试**，实现了从HTTP接口到WebSocket流式响应的完整迁移。系统目前仅支持原生WebSocket实现，Socket.IO框架虽然存在但已禁用。流式响应测试已稳定运行，但尚未开发付费功能模块。

## 全局架构图

```
┌─────────────────────────────────────────────────────────────────────────┐
│                        微信小程序客户端                                  │
│  ┌───────────────┐    ┌───────────────┐     ┌───────────────────────┐   │
│  │  用户界面层   │    │  数据处理层   │     │     通信管理层        │   │
│  │  (WXML/WXSS) │    │ (JS业务逻辑)  │     │ (WebSocket连接管理)   │   │
│  └───────┬───────┘    └───────┬───────┘     └──────────┬────────────┘   │
└──────────┼──────────────────┼───────────────────────┼─────────────────┘
           │                  │                       │
           ▼                  ▼                       ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                              HTTP/WebSocket                              │
└─────────────────────────────────────────────────────────────────────────┘
           │                  │                       │
           ▼                  ▼                       ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                         服务器端 (Node.js)                               │
│                                                                         │
│  ┌───────────────────────┐      ┌──────────────────────────────────┐    │
│  │      Express 服务     │      │       WebSocket 服务             │    │
│  │  ┌─────────────────┐  │      │  ┌─────────────────────────────┐ │    │
│  │  │  HTTP API 路由  │  │      │  │     事件控制管理器          │ │    │
│  │  └────────┬────────┘  │      │  └──────────┬──────────────────┘ │    │
│  └───────────┼───────────┘      └─────────────┼───────────────────┘     │
│              │                                │                          │
│              ▼                                ▼                          │
│  ┌──────────────────────────────────────────────────────────────┐       │
│  │                       业务逻辑层                              │       │
│  │  ┌─────────────────┐    ┌─────────────────┐    ┌──────────┐  │       │
│  │  │非流式分析服务   │    │流式分析服务     │    │请求锁机制│  │       │
│  │  └────────┬────────┘    └────────┬────────┘    └────┬─────┘  │       │
│  └───────────┼──────────────────────┼─────────────────┼──────────┘       │
│              │                      │                 │                  │
│              ▼                      ▼                 ▼                  │
│  ┌──────────────────────────────────────────────────────────────┐       │
│  │                       基础服务层                              │       │
│  │  ┌─────────────────┐    ┌─────────────────┐    ┌──────────┐  │       │
│  │  │ DeepSeek API   │    │  日志系统       │    │缓存管理  │  │       │
│  │  └─────────────────┘    └─────────────────┘    └──────────┘  │       │
│  └──────────────────────────────────────────────────────────────┘       │
│                                                                         │
└─────────────────────────────────────────────────────────────────────────┘
           │                  │                       │
           ▼                  ▼                       ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                        外部服务 & 存储                                  │
│  ┌────────────────┐  ┌────────────────┐  ┌────────────────────────┐    │
│  │  DeepSeek LLM  │  │  文件存储      │  │  (未来) 支付服务       │    │
│  └────────────────┘  └────────────────┘  └────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────┘
```

### 数据流向图

```
┌───────────┐     WebSocket连接     ┌───────────┐      API调用      ┌───────────┐
│           │─────────────────────▶│           │──────────────────▶│           │
│  小程序   │                      │  服务器   │                   │ DeepSeek  │
│  客户端   │◀─────────────────────│           │◀──────────────────│   LLM     │
│           │     流式数据响应     │           │     流式响应      │           │
└───────────┘                      └───────────┘                   └───────────┘
                                        │
                                        │ 结果存储
                                        ▼
                                   ┌───────────┐
                                   │ 文件系统  │
                                   │ 缓存存储  │
                                   └───────────┘
```

## 系统架构

### 后端架构

```
server/
├── app.js                 # 应用入口，整合HTTP和WebSocket服务
├── config.js              # 应用配置
├── services/
│   ├── deepseek.js        # 原有非流式分析服务
│   ├── stream-deepseek.js # 流式分析服务
│   ├── native-websocket.js # 原生WebSocket服务（已实现心跳机制）
│   ├── websocket.js       # Socket.IO服务管理（!!!目前禁用）
│   └── request-lock.js    # 请求锁机制，防止重复处理
├── utils/
│   ├── python-bazi.js     # 八字计算工具
│   ├── socket-client.js   # WebSocket客户端封装
│   └── logger.js          # 日志系统
├── controllers/           # 控制器层
├── middlewares/           # 中间件
│   ├── auth.js            # 认证中间件
│   └── validator.js       # 请求验证中间件
├── config/                # 配置文件目录
├── bazi-py/               # Python八字计算脚本
└── routes/
    └── api.js             # HTTP API路由
```

### 前端架构（微信小程序）

```
miniprogram-gift/
├── app.js                 # 小程序入口文件
├── app.json               # 小程序全局配置
├── app.wxss               # 小程序全局样式
├── config.js              # 环境配置文件（包含API和WebSocket地址）
├── utils/                 # 工具函数目录
│   ├── socket-manager.js  # WebSocket连接管理
│   ├── api.js             # HTTP API调用封装
│   ├── logger.js          # 日志和调试工具
│   └── formatter.js       # 数据格式化工具
├── behaviors/             # 共享行为
│   ├── stream-render-behavior.js  # 流式渲染行为（重要）
│   └── page-behavior.js   # 页面共享行为
├── pages/                 # 小程序页面
│   ├── index/             # 首页
│   ├── input/             # 输入信息页
│   ├── analysis/          # 分析结果页（接收流式数据）
│   ├── payment/           # 支付页面（未实现）
│   └── user/              # 用户页面
├── components/            # 自定义组件
│   ├── stream-content/    # 流式内容渲染组件
│   ├── loading/           # 加载指示组件
│   └── stream-display/    # 流式展示组件
└── libs/                  # 第三方库
    └── reconnecting-websocket.js  # 支持重连的WebSocket库
```

### 微信小程序关键模块

1. **WebSocket连接管理（v1.3核心改进）**
   - 负责建立和维护WebSocket连接
   - 实现断线自动重连机制
   - 管理心跳包发送
   - 处理通信状态

2. **流式渲染行为**
   - 实现流式数据接收和处理
   - 管理渲染状态和更新视图
   - 负责分段处理大型分析结果

3. **状态管理**
   - 跨页面数据共享
   - 分析状态追踪
   - 用户会话管理

### 数据流向

1. **HTTP流程**（已完成并保留向后兼容）：
   - 客户端请求 → API路由 → deepseek非流式分析 → 返回完整结果

2. **WebSocket流程**（v1.3版本已完成并稳定）：
   - 客户端WebSocket连接 → websocket服务 → stream-deepseek流式分析 → 流式返回结果

### 混合响应模式（v1.3已完全实现）

1. **检查现有结果**：
   - 根据infoHash检查临时文件是否存在
   - 如果存在，直接返回结果
   - 如果不存在，进行新分析

2. **两阶段分析**（流程已实现，但付费功能尚未开发）：
   - 第一阶段：基础分析，流式返回
   - 第二阶段：大运流年分析（当前版本尚未实现支付验证）

客户端 ⟷ WebSocket连接 ⟷ 服务器
                        ↓
  ┌──────────────────────────────────────┐
  │        事件控制管理器                │
  └──────────────────────────────────────┘
    ↓                              ↓
  ┌─────────────┐              ┌─────────────┐
  │ 第一阶段分析 │              │ 第二阶段分析 │
  └─────────────┘              └─────────────┘
    ↓                              ↓
  ┌─────────────────────────────────────────┐
  │               结果存储                  │
  └─────────────────────────────────────────┘

### 统一响应格式（已实现）

WebSocket和HTTP API使用统一的响应格式：

```javascript
// 通用响应格式
{
  code: 200,          // 与HTTP状态码保持一致
  message: "消息描述",  // 描述性文本
  success: true,      // 操作是否成功
  data: { ... }       // 响应数据
}
```

WebSocket事件类型：
1. `response` - 通用响应，对应HTTP请求响应
2. `analysisStatus` - 分析状态更新事件
3. `analysisChunk` - 流式分析数据块事件

## 开发完成度（v1.3）

### 已完成功能

- [x] 原有HTTP API `/api/v1/reasoner` 接口实现
- [x] 非流式DeepSeek分析服务
- [x] 流式DeepSeek分析服务
- [x] 原生WebSocket服务实现
- [x] 混合响应模式完整实现（检查现有结果、发起流式分析）
- [x] 统一HTTP和WebSocket响应格式
- [x] 两阶段分析流程框架实现
- [x] 请求锁机制防重复处理
- [x] 完善的错误处理和日志系统
- [x] 客户端-服务器心跳机制
- [x] **断线重连机制优化**（v1.3新增）
- [x] **WebSocket连接稳定性改进**（v1.3新增）
- [x] **小程序环境特定问题处理**（v1.3新增）

### 进行中功能

- [ ] 微信小程序端适配
  - [x] 基础WebSocket连接实现
  - [x] 流式结果处理和展示
  - [x] 断线重连机制（v1.3已完成）

- [ ] 系统优化
  - [x] 结果缓存机制
  - [x] 心跳机制保持连接活跃
  - [x] WebSocket连接稳定性改进（v1.3已完成）
  - [x] 小程序环境特定问题处理（v1.3已完成）

### 待开发功能

- [ ] 付费功能实现
  - [ ] 支付集成架构
  - [ ] 第二阶段分析付费触发机制
  - [ ] 付费状态管理
  
- [ ] 高级特性
  - [ ] 用户权限管理
  - [ ] 多模型分析支持

## 当前迭代重点

1. **付费功能开发**：
   - 实现支付集成架构
   - 开发第二阶段分析付费触发机制
   - 实现付费状态管理


2. **性能与安全**：
   - 优化高并发下的系统性能
   - 增强系统安全性
   - 完善监控告警机制

## 已解决问题（v1.3）

1. **WebSocket连接稳定性**：
   - 已解决第一阶段分析完成后连接意外关闭问题
   - 修复页面重新加载时的初始化错误
   - 改进`stream-render-behavior.js`中的初始化逻辑

2. **小程序环境限制**：
   - 已解决小程序切换前后台导致连接断开的问题
   - 实现了网络波动下的自动重连机制
   - 优化了状态管理和错误处理

## 后续迭代计划

1. **v1.4**：实现基础付费功能
   - 集成微信支付
   - 实现付费状态管理
   - 开发第二阶段分析付费触发机制

2. **v1.5**：完善用户管理
   - 实现用户注册登录
   - 开发用户权限管理
   - 添加用户数据分析

3. **v2.0**：功能拓展
   - 添加更多分析模型支持
   - 优化分析结果展示
   - 增强系统整体性能

---

## 部署情况

当前部署平台：微信云托管
部署环境：测试环境（无需备案）
部署状态：已完成

### 微信云托管部署架构

```
┌────────────────────────────────────────────────────────────────┐
│                      微信开发者平台                             │
│                                                                │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                     微信云托管                          │   │
│  │                                                         │   │
│  │  ┌─────────────────┐       ┌──────────────────────┐    │   │
│  │  │   服务实例      │       │    自动扩缩容        │    │   │
│  │  │  (Node.js容器)  │◀─────▶│    (0-5个实例)      │    │   │
│  │  └─────────────────┘       └──────────────────────┘    │   │
│  │           │                                            │   │
│  │           ▼                                            │   │
│  │  ┌──────────────────────────────────────────────┐     │   │
│  │  │               日志服务                       │     │   │
│  │  └──────────────────────────────────────────────┘     │   │
│  │                                                        │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                  │
│                              ▼                                  │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                     CDN加速                             │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                  │
└──────────────────────────────┼──────────────────────────────────┘
                               │
                               ▼
┌──────────────────────────────────────────────────────────────────┐
│                         用户小程序                               │
└──────────────────────────────────────────────────────────────────┘
```

### 部署配置文件

**Dockerfile**
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
ENV NODE_ENV=production
EXPOSE ${WX_PORT}
CMD ["node", "app.js"]
```

**container.config.json**
```json
{
  "containerPort": 80,
  "minNum": 0,
  "maxNum": 5,
  "cpu": 0.25,
  "mem": 0.5,
  "policyType": "cpu",
  "policyThreshold": 60,
  "envParams": {},
  "customLogs": "stdout"
}
```

### 部署优势

1. **无需备案**：微信云托管无需域名备案即可使用
2. **自动扩缩容**：根据流量自动调整实例数量
3. **无服务器运维**：无需维护服务器，减少运维成本
4. **天然小程序集成**：与微信生态无缝集成

---

## 迁移策略

第一阶段（保留兼容性）:
✅ 添加WebSocket基础设施
✅ 实现第一阶段流式输出
✅ 保留现有REST API功能

第二阶段（添加付费结构）:
⏳ 实现付费服务（计划中）
⏳ 修改第二阶段分析触发逻辑（计划中）
⏳ 添加支付相关API（计划中）

第三阶段（全面迁移）:
✅ 完全切换到WebSocket流式输出
⏳ 完善付费功能（计划中）

*最后更新时间: 2024年5月13日*