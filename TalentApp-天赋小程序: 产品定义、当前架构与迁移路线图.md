# TalentApp/天赋小程序: 产品定义、当前架构与迁移路线图
## 1. 产品概述 (初步构想: v1.0)
### 1.1. 产品定位

**解决了什么问题？**

绝大多数人在成长的过程中都或多或少缺乏对于自身天赋潜能和爱好的探索，对于“我这一生最适合做什么”相信每个人都有过疑问和困惑。造成这些困扰的原因是多方面的，有社会因素，也有家庭因素和个人因素。总体而言，探索天赋是一件需要高成本的事情，原生家庭的悉心引导，适逢导师的引路和启迪，个人在探索过程中必须要付出的时间和金钱成本，都使得每个人在成长的过程中寻找到自己真正喜欢并且适合的事情往往不那么容易。本产品旨在利用玄学+统计+AI大模型的跨界技术整合，实现对个体天赋的低成本有效探索。

**目标用户**

年龄段 18—45岁，特别是处于升学/转专业/求职/更换行业或岗位/创业/副业的青年人或者对于自身事业发展有困惑的人。

### 1.2. 核心价值
一般玄学软件没有解读或者解读准确性极低且文案风格呆滞无聊，我们希望提供由AI大模型（deepseek满血版）解读并且整理后的文案输出，在对用户问题解决的参考价值和阅读趣味性上都更胜一筹，让用户在一种轻松诙谐的氛围中便可习得对自己有帮助的建议和参考。

### 1.3. 实用场景
C端用户直接在微信小程序上在线体验。

### 1.4. 版本说明 
**v1.0 (概念版本)**

- 此版本专注于特定垂直领域的功能实现。后续计划扩展至婚恋感情、求子、贵人等领 域（属于他人关系领域）。

- 当前垂直领域焦点: 初期聚焦于个体自身的发展，包含天赋性格、事业、财富、迁移、健康这五个方面。此垂直思路参考紫微斗数本命宫会照三方四宫，关于自身的课题。

**产品概览**

| 模块          | 说明                                                               |
| ----------- | ---------------------------------------------------------------- |
| **产品定位**    | 利用 *玄学 + 统计 + AI LLM* 帮助用户低成本探索天赋潜能，主要面向 18‑45 岁在升学/转职/创业节点困惑的用户 |
| **核心价值**    | 以 DeepSeek LLM 生成的高可读性文案替代传统玄学软件生硬输出，提供更有趣、更实用的建议                |
| **v1.0 范围** | 专注“个体自身发展”五宫：天赋性格 / 事业 / 财富 / 迁移 / 健康                            |
| **关键流程**    | 首页引导 → 信息收集（生日/时间/地点/性别）→ 调用分析 API → 分步渲染结果                      |
| **未来扩展**    | 婚恋、合伙、子女、贵人等他人关系领域；星盘、紫薇斗数、多模型混合分析                               |


***

## 2. 功能详解
### 2.1. 功能模块

**用户信息预处理**

- 添加八字四柱 + 大运 + 流年 

- 紫微斗数命盘 + 流盘

- 星座出生盘（宫位、行星等 - 待补充）

**DeepSeek API 调用**

- chat-v3模型（娱乐引流）

_系统Prompt_：你是一位专业的人生规划导师，尤其擅长各类东西方玄学，如八字紫薇斗数星座奇门遁甲等。请尽可能开发用户的天赋，引导其人生最大潜能的释放，言语尽可能客观有趣，不用太温和。

_模版Prompt_：性别${gender}，公历出生日期是${year}年${month}月${day}日${hour}时${minute || 0}分。${yearColumn}年柱 ${monthColumn}月柱 ${dayColumn}日柱 ${timeColumn}时柱。大运：${dayunInfo}。近期流年：${liunianInfo}。请基于此人八字命盘，分析此人的天赋性格，体貌特征，最适合从事哪些行业以及适合的岗位，适合在哪里发展，事业的大小，身体健康状况，再着重分析大运大富大贵的可能性，如何发挥最好的天赋和潜能。

- reasoner-r1模型（付费）

_系统Prompt_：逻辑严密，往命理格局的最高层次指引

第一轮：

_basicPrompt_：性别${gender}，公历出生日期是${year}年${month}月${day}日${hour}时${minute || 0}分。${yearColumn}年柱 ${monthColumn}月柱 ${dayColumn}日柱 ${timeColumn}时柱。大运：${dayunInfo}。近期流年：${liunianInfo}。

_guidancePrompt_：请基于此人八字命盘，往命理格局的最高层次指引，分析此人的天赋性格，体貌特征，最适合从事哪些行业以及适合的岗位，适合在哪里发展，事业的大小，身体健康状况。

_模版Prompt_：${basicPrompt}\n${guidancePart}

第二轮：

_模版Prompt:_ {basicPrompt}\n基于以下八字第一轮分析结果，进一步分析未来大运和流年的关键机遇与挑战，重点关注事业发展和财富量级，尽可能客观理性，不要过度解读：\n\n${firstResponse.content}

Prompt 模版的迭代优化：需要根据后期市场需求来设计

比如：多轮对话场景 AI引导式提问互动

### 2.2. 界面说明 (微信小程序)

**首页 (引导页)**

- 简单介绍小程序的功能，例如“测测你的真实天赋是什么？”

- 提示用户点击“开始”按钮，开始引导流程。

**第二页 (用户信息输入页 )**

- 用户出生日期（包含年月日信息）

- 用户出生日的准确时间（包含时分信息）

- 用户出生地点（真太阳时校准，目前没有夏令时调整）

- 用户性别

引导用户信息确认，用户点击“天赋分析”按钮，小程序调起一个API请求去分析数据。

**第三页 (结果返回页面)**

- 服务器返回结果之前loading页面显示“AI正在玩命分析中，请耐心等待...” （还未添加）

- 服务器返回结果后按照一定的输出样式逐行进行**流式**输出。

- 输出完成后提示用户点击“重新测试”按钮跳转回第二页。

***

## 3. 当前系统架构与开发进度 (v1.3)

### 3.1. 当前项目概述

当前版本**v1.3**通过DeepSeek LLM提供分析服务,实现了从HTTP接口到WebSocket流式响应的完整迁移。系统目前仅支持原生WebSocket实现，Socket.IO框架虽然存在但已禁用。流式响应测试已稳定运行并成功部署线上内测体验版，尚未开发付费功能模块。

### 3.2. 全局架构图 (v1.3)

```
┌──────────────────────────────────────────────────────────────────────┐
│                        微信小程序客户端                                 │
│  ┌───────────────┐    ┌───────────────┐     ┌─────────────────────┐  │
│  │  用户界面层     │    │  数据处理层     │     │     通信管理层       │  │
│  │  (WXML/WXSS)  │    │ (JS业务逻辑)    │     │ (WebSocket连接管理)  │  │
│  └───────┬───────┘    └───────┬───────┘     └─────────┬──────────┘   │
└──────────┼──────────────────--┼───────────────────────┼──────────────┘
           │                    │                       │
           ▼                    ▼                       ▼
┌───────────────────────────────────────────────────────────────────────┐
│                              HTTP/WebSocket                           │
└───────────────────────────────────────────────────────────────────────┘
           │                  │                       │
           ▼                  ▼                       ▼
┌──────────────────────────────────────────────────────────────────────┐
│                         服务器端 (Node.js)                            │
│                                                                      │
│  ┌───────────────────────┐      ┌───────────────────────────────┐    │
│  │      Express 服务     │       │       WebSocket 服务           │   │
│  │  ┌─────────────────┐  │      │  ┌─────────────────────────┐  │    │
│  │  │  HTTP API 路由   │  │      │  │     事件控制管理器        │  │    │
│  │  └────────┬────────┘  │      │  └──────────┬──────────────┘  │    │
│  └───────────┼───────────┘      └─────────────┼─────────────────┘    │
│              │                                │                      │
│              ▼                                ▼                      │
│  ┌────────────────────────────────────────────────────────────┐      │
│  │                       业务逻辑层                             │      │
│  │  ┌─────────────────┐    ┌─────────────────┐    ┌─────────┐  │     │
│  │  │非流式分析服务      │    │流式分析服务       │    │请求锁机制│  │     │
│  │  └────────┬────────┘    └────────┬────────┘    └───┬────┘   │     │
│  └───────────┼──────────────────────┼─────────────────┼────────┘     │
│              │                      │                 │              │
│              ▼                      ▼                 ▼              │
│  ┌─────────────────────────────────────────────────────────────┐     │
│  │                       基础服务层                              │    │
│  │  ┌─────────────────┐    ┌─────────────────┐    ┌──────────┐  │   │
│  │  │ DeepSeek API    │    │  日志系统         │    │缓存管理   │  │   │
│  │  └─────────────────┘    └─────────────────┘    └──────────┘  │   │
│  └──────────────────────────────────────────────────────────────     │
│                                                                      │
└─────────────────────────────────────────────────────────────────────┘
           │                  │                       │
           ▼                  ▼                       ▼
┌───────────────────────────────────────────────────────────────────────┐
│                        外部服务 & 存储                                  │
│  ┌────────────────┐  ┌────────────────┐  ┌────────────────────────┐   │
│  │  DeepSeek LLM  │  │  文件存储        │  │  (未来) 支付服务        │    │
│  └────────────────┘  └────────────────┘  └────────────────────────┘   │
└───────────────────────────────────────────────────────────────────────┘
```

### 3.3. 数据流向图 (v1.3 - WebSocket 流式分析)

```
┌───────────┐     WebSocket连接     ┌───────────┐      API调用      ┌───────────┐
│           │─────────────────────▶│           │──────────────────▶│           │
│  小程序    │                      │  服务器    │                   │ DeepSeek  │
│  客户端    │◀─────────────────────│           │◀──────────────────│   LLM     │
│           │     流式数据响应       │           │     流式响应       │           │
└───────────┘                      └───────────┘                   └───────────┘
                                        │
                                        │ 结果存储
                                        ▼
                                   ┌───────────┐
                                   │ 文件系统   │
                                   │ 缓存存储   │
                                   └───────────┘
```
                                   
1. HTTP流程(已完成但当前版本不启用): 客户端请求 → API路由 → deepseek非流式分析 → 返回完整结果。

2. WebSocket流程 (v1.3版本已完成并稳定): 客户端WebSocket连接 → WebSocket服务 → stream-deepseek流式分析 → 流式返回结果。
                                              

### 3.4. 系统架构

#### 3.4.1. 后端架构 (Node.js)
```
server/
├── app.js                 # 应用入口，整合HTTP和WebSocket服务
├── config.js              # 应用配置
├── services/
│   ├── deepseek.js        # 原有非流式分析服务
│   ├── stream-deepseek.js # 流式分析服务
│   ├── native-websocket.js # 原生WebSocket服务（已实现心跳机制）
│   ├── websocket.js       # Socket.IO服务管理（!!!目前禁用）
│   └── request-lock.js    # 请求锁机制，防止重复处理
├── utils/
│   ├── python-bazi.js     # 八字计算工具
│   ├── socket-client.js   # WebSocket客户端封装
│   └── logger.js          # 日志系统
├── controllers/           # 控制器层
├── middlewares/           # 中间件
│   ├── auth.js            # 认证中间件
│   └── validator.js       # 请求验证中间件
├── config/                # 配置文件目录
├── bazi-py/               # Python八字计算脚本
└── routes/
    └── api.js             # HTTP API路由
```

#### 3.4.2. 前端架构 (微信小程序)
```
miniprogram-gift/
├── app.js                 # 小程序入口文件
├── app.json               # 小程序全局配置
├── app.wxss               # 小程序全局样式
├── config.js              # 环境配置文件（包含API和WebSocket地址）
├── utils/                 # 工具函数目录
│   ├── socket-manager.js  # WebSocket连接管理
│   ├── api.js             # HTTP API调用封装
│   ├── logger.js          # 日志和调试工具
│   └── formatter.js       # 数据格式化工具
├── behaviors/             # 共享行为
│   ├── stream-render-behavior.js  # 流式渲染行为（重要）
│   └── page-behavior.js   # 页面共享行为
├── pages/                 # 小程序页面
│   ├── index/             # 首页
│   ├── input/             # 输入信息页
│   ├── analysis/          # 分析结果页（接收流式数据）
│   ├── payment/           # 支付页面（未实现）
│   └── user/              # 用户页面
├── components/            # 自定义组件
│   ├── stream-content/    # 流式内容渲染组件
│   ├── loading/           # 加载指示组件
│   └── stream-display/    # 流式展示组件
└── libs/                  # 第三方库
    └── reconnecting-websocket.js  # 支持重连的WebSocket库
```

### 3.5. 微信小程序v1.3核心改进模块

 **1. WebSocket连接管理**

- 负责建立和维护WebSocket连接

- 实现断线自动重连机制

- 管理心跳包发送

- 处理通信状态

**2. 流式渲染行为**

- 实现流式数据接收和处理

- 统一管理渲染状态和更新视图

**3. 状态管理**

- 跨页面数据共享

- 分析状态追踪

- 用户会话管理

### 3.6 技术细节说明

#### 3.6.1 混合响应模式

检查现有结果 --> 根据infoHash检查临时文件是否存在 --> 如果存在，直接返回结果 --> 如果不存在，进行新分析，避免反复调用第三方API，节约资源降低成本。

#### 3.6.2 两阶段分析

第一阶段：推理首轮分析，流式返回。

第二阶段：大运流年分析（当前版本尚未实现支付验证）。
```
客户端 ⟷ WebSocket连接 ⟷ 服务器
                        ↓
  ┌──────────────────────────────────────┐
  │        事件控制管理器                   │
  └──────────────────────────────────────┘
    ↓                              ↓
  ┌─────────────┐              ┌─────────────┐
  │ 第一阶段分析  │              │ 第二阶段分析  │
  └─────────────┘              └─────────────┘
    ↓                              ↓
  ┌─────────────────────────────────────────┐
  │               结果存储                    │
  └─────────────────────────────────────────┘
 ```
  
目前拆分了第一阶段和第二阶段的异步函数逻辑，两阶段现在保持独立事件触发机制。

#### 3.6.3 统一响应格式

1. WebSocket和HTTP API使用统一的响应格式：
```
// 通用响应格式
{
  code: 200,          // 与HTTP状态码保持一致
  message: "消息描述",  // 描述性文本
  success: true,      // 操作是否成功
  data: { ... }       // 响应数据
}
```
2. WebSocket事件类型：

- response - 通用响应，对应HTTP请求响应。

- analysisStatus - 分析状态更新事件。

- analysisChunk - 流式分析数据块事件。

### 3.7. 开发完成进度表

**已完成功能**

- [x] 原有HTTP API /api/v1/reasoner 接口实现 
- [x] 非流式DeepSeek分析服务 
- [x] 流式DeepSeek分析服务
- [x] 原生WebSocket服务实现
- [x] 混合响应模式完整实现（检查现有结果、发起流式分析）
- [x] 统一HTTP和WebSocket响应格式
- [x] 两阶段分析流程框架实现 
- [x] 流式结果处理和展示
- [x] 请求锁机制防重复处理
- [x] 完善的错误处理和日志系统
- [x] 客户端-服务器心跳机制保持连接活跃
- [x] 断线重连机制优化
- [x] WebSocket连接稳定性改进
- [x] 小程序环境特定问题处理(websocket断开或者早停并非前端或者后端主动关停)

**待修复功能**

[ ] 获取地点经纬度信息功能放到后台服务端，避免前端API-key暴露
[ ] 本地地理经纬度映射表维护
[ ] 第二阶段分析结果页面_handleResponse()函数覆盖behaviour组件的同名函数
[ ] submissionID字段未启动（用户关联唯一ID）
[ ] UI设计更改（参考用户反馈）

**待开发功能**

[ ] 支付集成架构
[ ] 付费功能实现
[ ] 付费状态管理
[ ] 用户登录系统/权限管理
[ ] 数据库/数据隐私/脱敏
[ ] 内容输出合规性/符合监管审查
[ ] MBTI/星座等平行功能模块开发
[ ] 多模型分析支持

### 3.8. 当前迭代重点 (v1.3之后)

**第一阶段用户系统**

- 完善用户管理
  1. 实现用户注册登录
  2. 添加用户数据预处理（所有玄学模块参考测测app）
- 数据库
- 移交前端处理用户经纬度数据的逻辑至后端

**第二阶段集成支付**

- 大运流年事件分析付费触发机制
- 集成微信支付

**第三阶段正式上线版本**

- 合规审查/数据脱敏/域名备案 **难度较大周期较长**
- 高并发测试/压力测试
- 性能与安全优化

### 3.9. 部署情况

当前部署平台: 微信云托管。

部署环境: 测试环境（无需备案）。

部署状态: 已完成。

#### 3.9.1. 微信云托管部署架构图
```
┌───────────────────────────────────────────────────────────┐
│                      微信开发者平台                         │
│                                                           │
│  ┌───────────────────────────────────────────────────┐    |  
│  │                     微信云托管                      │    │
│  │                                                    │   |
│  │  ┌─────────────────┐       ┌─────────────────┐     │   │
│  │  │   服务实例        │       │   自动扩缩容     │     │   │
│  │  │  (Node.js容器)   │◀─────▶    (0-5个实例)    │     │   │
│  │  └─────────────────┘       └──────────────────┘    │   │
│  │           │                                        │   │
│  │           ▼                                        │   │
│  │  ┌────────────────────────────────────────────┐    │   │
│  │  │               日志服务                      │    │   │
│  │  └────────────────────────────────────────────┘    │   │
│  │                                                    │   │
│  └────────────────────────────────────────────────────┘   │
│                              │                            │
│                              ▼                            │
│  ┌────────────────────────────────────────────────────┐    │
│  │                     CDN加速                         │    │
│  └────────────────────────────────────────────────────┘    │
│                              │                             │
└──────────────────────────────┼─────────────────────────────┘
                               │
                               ▼
┌────────────────────────────────────────────────────────────┐
│                         用户小程序                           │
└────────────────────────────────────────────────────────────┘
```
#### 3.9.2. 部署优势

- **无需备案** 

微信云托管无需域名备案即可使用。

- **自动扩缩容**

根据流量自动调整实例数量 (0-5个实例)。

- **无服务器运维**

无需维护服务器，减少运维成本。

- **天然小程序集成** 

与微信生态无缝集成。

#### 3.9.3. 当前部署遗留问题

- 目前缓存机制使用临时文件，云托管空间有限，后续没法维护

- 本地测试和云托管联动现象

- 云托管根据流量自动回收资源服务器连接不稳定造成websocket连接失败

### 3.10 线上体验版用户反馈

- 不需要暴露deepseek

- deepseek返回字段前添加loading提示/动画 优化等待空白

- 页面太长字太小

***

## 4. 微信小程序 → 全球多端 App 迁移技术路线图 (Draft)

### 4.1 迁移条件

#### 4.1.1 何时 “一定” 要从微信小程序升级到独立 App？

| 触发条件    | 典型场景 | 为什么小程序做不到/做不好？ | 迁移到 App 的直接收益
|:-----------|:--------|:-----------------------|:-------------------|
| 系统级能力依赖  |• 持续后台推送（每日多次提醒、订阅制课程）<br>• 深度本地通知、日历写入、联系人读取   |  小程序后台存活≈5 min；订阅消息频次受限，无法常驻  |• APNs / FCM 长期推送 • iOS/Android 原生权限全面开放
|重度 3D / 本地 AI 推理	|• 3D 星盘实时旋转<br>• On-device LLM (ggml / MLC)|小程序 WebGL、WASM 性能阈值低；包体 10 MB + 分包 2 MB 限制；禁止大规模二进制	| • 引入 Metal / Vulkan 渲染管线<br>• 搭载 1 ~ 2 GB 量化模型离线推理|
| 全球市场 & 多渠道获客  |• Google Play / App Store 关键词自然流量<br> • 与 TikTok、Meta Ads 垂直转化  | 海外用户不一定使用/信任微信生态；微信支付受地域限制 |• 独立品牌曝光 + ASO/ASA <br>• Stripe/PayPal 覆盖 200+ 地区
| 商业模式超越微信支付 | • 订阅制 (Recurring Billing)<br> • Bundle 内购、Family Sharing| 微信支付只支持一次性支付，自动续费须跳转公众号 | • 支持 App Store In-App Purchase、Google Billing 订阅<br>• 统一多币种、分账
| 合规性    | 产品发布上线 | 小程序内容不过审/域名备案失败 | 无玄学产品合规性成本

**边界性判断 checklist (任选一条满足即可启动迁移)**

1. 功能需求

   - 需要>1 小时后台存活或精准定时推送
   - 需要嵌入自研 3D 引擎 / ARKit / 整机 AI 推理

2. 市场战略

   - 目标用户 ≥ 30 % 来自非微信高渗透市场（如北美、欧盟、印尼）
   - 预计 App Store / Play 下载带来的获客 ROI > 微信广告 ROI

3. **支付&合规**

   - 订阅制 LTV 方案已在商业模型评审通过
   - 数据合规需 EU 数据本地化，且微信云托管无公网节点可选
   - **支持数字货币付费**

4. 性能体验

   - 次日留存受限于加载首帧 > 3 s（小程序包体被硬性拆分）
   - 交互动画在小程序 FPS < 45，需要原生渲染
   
#### 4.1.2. 判断核心

一旦**核心功能 x 商业模式 x 合规支付**有单点刚需超出微信生态上限，就应果断进入 App 迁移按键，如果目前全部为“否”或“低优先级” 应该继续深耕小程序。小程序在低成本验证 MVP、快速迭代、与微信生态闭环 (分享 / 支付) 上仍有巨大优势，可以继续利用小程序的低门槛与社交流量，把资源投入在算法、内容与留存运营上。

---

### 4.2. 迁移概览

>**定位:** 在保持现有 Node.js + WebSocket 流式核心的前提下，将产品从微信小程序平滑扩展至 React Native / Flutter App 与 Web SPA，并支持多区域合规、付费变现及后续本地 LLM 增强。

**核心原则:** 

增量升级、模块化、兼容灰度、数据安全、全球合规、多币种支付。

#### 4.2.1 战略目标(可持续修正)

| 维度        | 目标                              | 指标/KPI                         |
| --------- | ------------------------------- | ------------------------------ |
| **体验一致**  | 小程序、RN App、Web SPA 三端交互与分析结果一致性 | 95 % ± 1 token 差异、首屏渲染 < 1.8 s |
| **全球可访问** | 覆盖主要流量区（SGP / FRA / SFO）        | TTFB < 300 ms（95th）            |
| **商业闭环**  | 支付‑分析‑报告全链路闭环                   | 二阶段付费转化 ≥ 15 %，退款率 ≤ 2 %       |
| **合规安全**  | GDPR / PDPA / 微信合规              | 外部审计零高危，日志脱敏 100 %             |

#### 4.2.2 能力差距分析

| 层级     | v1.3 现状                      | 迁移缺口                               |
| ------ | ---------------------------- | ---------------------------------- |
| **前端** | 仅微信小程序；WebSocket 封装耦合 wx API | 缺跨端 SDK、RN UI、Web 入口               |
| **后端** | Node.js + 原生 WS；无持久化用户表      | 需要 Auth + DB + Geo 服务 + 支付 + 多区域部署 |
| **数据** | 临时文件缓存，云托管存储受限               | 关系型 DB、对象存储、Redis 集群               |
| **运维** | 微信云托管单区                      | Docker + ECS/GKE，多区域蓝绿发布           |

#### 4.2.3 阶段里程碑

| 阶段                | 关键交付                                      | 负责人             | 计划(周) | 验收指标                          |
| ----------------- | ----------------------------------------- | --------------- | ----- | ----------------------------- |
| **P0 强化 v1.3**    | *Bug Fix + refactor*；抽离配置、补单测             | BE A            | 1     | 单元测试覆盖 ≥ 60 %                 |
| **P1 用户体系 & Geo** | Auth JWT、`users` 表、Geo Service            | BE A、DB B       | 2     | 新注册成功率 ≥ 98 %；经纬度命中 ≥ 90 %    |
| **P2 跨端 SDK**     | `/sdk/core`、adapters (weapp/rn/web)、CI 发布 | FE A            | 2     | 三端延迟差 < 150 ms；重连成功率 = 100 %  |
| **P3 功能扩展**       | 星座 / MBTI / 玛雅 / 紫薇斗数 流式服务                | Algo A          | 3     | P90 分析耗时 < 10 s；错误率 < 1 %     |
| **P4 商业闭环**       | Stripe/PayPal 接入、Webhook、订单表              | BE B            | 2     | 对账差异 < 0.1 %；付费 CVR ≥ 15 %    |
| **P5 RN Beta**    | RN App MVP、推送、TestFlight/Play Beta        | Mobile A        | 4     | 崩溃率 < 1 %；DAU 留存 +5 %         |
| **P6 高级功能**       | On‑device LLM (MLC), 3D 星盘                | Algo B、Mobile A | 6     | 首 token < 1.2 s；GPU 占用 < 40 % |

> **总周期**：20 周；缓冲 + 假期 = 22 周；**目标 Q4‑2025 正式版上线**。

#### 4.2.4 迭代周期(可根据实际开发情况调整)

> Phase 1 – 用户体系 & 地理编码

* 腾讯地图查询经纬度+本地地理经纬度映射表维护
* **Geo Service**：`/geo/lookup`
* 前端移除 QQMap Key
* **Auth Service**：JWT，支持微信 / 邮箱 / 手机
* **数据库**替换临时文件缓存机制

>Phase 2 – 跨端 Network SDK... (具体技术细节后续补充)

* `/sdk/core` 心跳 + 重连
* `sendRequest` / `sendStream` 统一接口
* npm 私包；CI 发布

>Phase 3 – 功能扩展

* 新增流式服务：星座 / MBTI / 玛雅 / 紫薇斗数
* 国际化; P90 耗时 < 10 s

>Phase 4 – 商业化

* Stripe / PayPal / 微信支付
* Webhook 更新订单 → 高级报告

>Phase 5 – 高级功能 & 本地 LLM

* MLC Runtime / ggml Edge 推理
* Three.js / Unity 3D 星盘渲染

### 4.3 全局技术架构（高层）

```
Clients (WeChat MP | React Native | Web SPA)
        │
Cross‑Platform Network SDK  ←→  Analytics SDK (可选离线推理)
        │        ▲
        ▼        │ WebSocket / HTTP Fallback
Global CDN & WAAP  (CloudFront/Cloudflare)
        │
Regional  ALB  (SGP | FRA | SFO)
        │
┌──────────────────── Node.js Container Cluster ────────────────────┐
│  Auth Service   |   Geo Service   |   Analysis Services (多模型)  │
│  Payment Webhook|   Media Proxy   |   Internal gRPC Bus          │
└───────────────────────────────────────────────────────────────────┘
        │                   │                │
   PostgreSQL (RDS)    Redis Cluster    S3 Object Storage
```

#### 4.3.1 技术栈（后期根据实际情况调整）

* **后端**：Node.js + Fastify/ws, PostgreSQL, Redis, S3, Docker, ECS/GKE
* **移动端**：React Native / Flutter, Expo
* **监控**：Prometheus, Grafana, Sentry

### 4.4 数据与合规策略

1. **数据驻留**：EU 用户数据落在 FRA 区；其余留在 SGP 默认区。
2. **最小权限**：分析容器只读公共对象存储；支付容器无 DB 写权限。
3. **隐私对齐**：注册流程弹窗 GDPR Art 6‑1‑b；日志用 SHA‑256 脱敏。
4. **备份策略**：DB 日备份 + 跨区冷备；对象存储 S3 Versioning。

### 4.5 风控与性能

| 风险           | 影响    | 缓解                            |
| ------------ | ----- | ----------------------------- |
| 第三方 LLM 限流   | 响应超时  | 余量账户 + 本地推理降级                 |
| WebSocket 阻断 | 服务不可用 | HTTP fallback + 重连 + 多 CDN IP |
| 支付欺诈         | 收益受损  | Stripe Radar + 指纹仓库           |
| App 审核驳回     | 上线延迟  | 分级内容开关 + A/B Flag             |

**！数据合规和风控性能都是产品相对后期才需要考虑的问题。需要找有经验的专业人士解决。**

### 4.6 资源预算（仅供参考）

| 角色          | 人周   | 单价(USD) | 成本           |
| ----------- | ---- | ------- | ------------ |
| Backend x2  | 20×2 | 800     | 32 k         |
| Frontend x2 | 14×2 | 700     | 19.6 k       |
| Mobile x2   | 18×2 | 750     | 27 k         |
| DevOps x1   | 12×1 | 900     | 10.8 k       |
| Design x1   | 6×1  | 600     | 3.6 k        |
| **合计**      |      |         | **93 k USD** |


> **Infra 预算**：AWS/GCP ≈ 7 k USD / 年（含三区流量 3TB、S3 1TB、RDS db.t3.medium x2）。

降低开发成本思路：可以考虑upwork网站等远程项目制形式发包结算，找在校学生或东欧小哥。

